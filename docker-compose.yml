version: '3.8'

services:
  # PostgreSQL Database
  db:
    image: postgres:15
    environment:
      POSTGRES_DB: knowledge_base
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5433:5432"

  # Redis for Celery broker and cache
  redis:
    image: redis:7-alpine
    ports:
      - "6380:6379"

  # Django Web Application
  web:
    build: ./backend
    command: python manage.py runserver 0.0.0.0:8000
    volumes:
      - ./backend:/app
      - media_files:/app/media
    ports:
      - "8001:8000"
    depends_on:
      - db
      - redis
    env_file:
      - ./backend/.env
    environment:
      - DEBUG=1
      - DATABASE_URL=**************************************/knowledge_base
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0

  # Celery Worker
  celery:
    build: ./backend
    command: celery -A knowledge_base worker --loglevel=info
    volumes:
      - ./backend:/app
      - media_files:/app/media
    depends_on:
      - db
      - redis
    env_file:
      - ./backend/.env
    environment:
      - DEBUG=1
      - DATABASE_URL=**************************************/knowledge_base
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0

  # Celery Beat (스케줄러)
  celery-beat:
    build: ./backend
    command: celery -A knowledge_base beat --loglevel=info
    volumes:
      - ./backend:/app
    depends_on:
      - db
      - redis
    env_file:
      - ./backend/.env
    environment:
      - DEBUG=1
      - DATABASE_URL=**************************************/knowledge_base
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0

  # Flower (Celery 모니터링)
  flower:
    build: ./backend
    command: celery -A knowledge_base flower --port=5555
    ports:
      - "5555:5555"
    depends_on:
      - redis
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0

volumes:
  postgres_data:
  media_files:
