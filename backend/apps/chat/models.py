from django.db import models
from django.contrib.auth.models import User
from apps.documents.models import Document


class ChatSession(models.Model):
    """채팅 세션 모델"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='chat_sessions')
    title = models.CharField(max_length=200, blank=True)
    document = models.ForeignKey(Document, on_delete=models.SET_NULL, null=True, blank=True,
                                related_name='chat_sessions')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        ordering = ['-updated_at']

    def __str__(self):
        return f"Chat Session {self.id} - {self.user.username}"

    def save(self, *args, **kwargs):
        if not self.title and self.document:
            self.title = f"{self.document.title}에 대한 채팅"
        elif not self.title:
            self.title = f"채팅 세션 {self.created_at.strftime('%Y-%m-%d %H:%M')}"
        super().save(*args, **kwargs)


class ChatMessage(models.Model):
    """채팅 메시지 모델"""
    SENDER_CHOICES = [
        ('user', '사용자'),
        ('ai', 'AI'),
    ]

    session = models.ForeignKey(ChatSession, on_delete=models.CASCADE, related_name='messages')
    sender = models.CharField(max_length=10, choices=SENDER_CHOICES)
    content = models.TextField()
    context = models.JSONField(null=True, blank=True, help_text="문서 컨텍스트 등 추가 정보")
    created_at = models.DateTimeField(auto_now_add=True)

    # AI 응답 관련 메타데이터
    response_time = models.FloatField(null=True, blank=True, help_text="AI 응답 생성 시간(초)")
    model_used = models.CharField(max_length=50, blank=True, help_text="사용된 AI 모델")
    tokens_used = models.IntegerField(null=True, blank=True, help_text="사용된 토큰 수")

    class Meta:
        ordering = ['created_at']

    def __str__(self):
        return f"{self.sender}: {self.content[:50]}..."
