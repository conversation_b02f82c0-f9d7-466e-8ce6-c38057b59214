from django.contrib import admin
from .models import EmbeddingConfig, EmbeddingStats


@admin.register(EmbeddingConfig)
class EmbeddingConfigAdmin(admin.ModelAdmin):
    list_display = ['name', 'chunk_size', 'chunk_overlap', 'embedding_model', 'is_active', 'updated_at']
    list_filter = ['is_active', 'embedding_model']
    search_fields = ['name']


@admin.register(EmbeddingStats)
class EmbeddingStatsAdmin(admin.ModelAdmin):
    list_display = ['total_documents', 'total_chunks', 'faiss_index_size', 'openai_index_size', 'last_updated']
    readonly_fields = ['last_updated']
