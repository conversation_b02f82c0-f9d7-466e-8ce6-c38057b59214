import os
import hashlib
import json
from django.db import models
from django.contrib.auth.models import User
from apps.core.models import BaseModel, Category, Tag


def document_upload_path(instance, filename):
    """문서 업로드 경로 생성"""
    from datetime import datetime

    # created_at이 None인 경우 현재 시간 사용
    if instance.created_at:
        year = instance.created_at.year
        month = instance.created_at.month
    else:
        now = datetime.now()
        year = now.year
        month = now.month

    return f'documents/{year}/{month}/{filename}'


class Document(BaseModel):
    """문서 모델"""
    DOCUMENT_TYPES = [
        ('pdf', 'PDF'),
        ('video', '동영상'),
        ('image', '이미지'),
        ('text', '텍스트'),
        ('audio', '오디오'),
        ('web', '웹페이지'),
        ('chat', '채팅'),
        ('other', '기타'),
    ]

    PROCESSING_STATUS = [
        ('pending', '대기중'),
        ('processing', '처리중'),
        ('completed', '완료'),
        ('failed', '실패'),
    ]

    title = models.CharField(max_length=200, verbose_name='제목')
    description = models.TextField(blank=True, verbose_name='설명')
    document_type = models.CharField(
        max_length=20, 
        choices=DOCUMENT_TYPES, 
        verbose_name='문서 타입'
    )
    
    # 파일 관련
    file = models.FileField(
        upload_to=document_upload_path,
        null=True,
        blank=True,
        verbose_name='파일'
    )
    file_size = models.BigIntegerField(null=True, blank=True, verbose_name='파일 크기')
    file_type = models.CharField(max_length=100, blank=True, verbose_name='파일 타입')
    file_hash = models.CharField(max_length=64, blank=True, verbose_name='파일 해시', db_index=True)
    thumbnail = models.FileField(
        upload_to='thumbnails/',
        null=True,
        blank=True,
        verbose_name='썸네일'
    )
    
    # 웹페이지 관련
    url = models.URLField(blank=True, verbose_name='URL')

    # 소스 정보 (Google Drive, Notion 등)
    source_type = models.CharField(max_length=50, blank=True, verbose_name='소스 타입')
    source_id = models.CharField(max_length=200, blank=True, verbose_name='소스 ID')
    source_url = models.URLField(blank=True, verbose_name='소스 URL')
    mime_type = models.CharField(max_length=100, blank=True, verbose_name='MIME 타입')

    # 텍스트 내용
    content = models.TextField(blank=True, verbose_name='내용')
    extracted_text = models.TextField(blank=True, verbose_name='추출된 텍스트')
    
    # 분류
    category = models.ForeignKey(
        Category, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        verbose_name='카테고리'
    )
    tags = models.ManyToManyField(Tag, blank=True, verbose_name='태그')
    
    # 처리 상태
    processing_status = models.CharField(
        max_length=20,
        choices=PROCESSING_STATUS,
        default='pending',
        verbose_name='처리 상태'
    )
    
    # 메타데이터
    metadata = models.JSONField(default=dict, blank=True, verbose_name='메타데이터')
    
    # 즐겨찾기
    is_favorite = models.BooleanField(default=False, verbose_name='즐겨찾기')
    
    # 조회수
    view_count = models.PositiveIntegerField(default=0, verbose_name='조회수')

    class Meta:
        verbose_name = '문서'
        verbose_name_plural = '문서'
        ordering = ['-created_at']

    def __str__(self):
        return self.title

    def save(self, *args, **kwargs):
        # 파일 정보 자동 설정
        if self.file:
            self.file_size = self.file.size
            self.file_type = os.path.splitext(self.file.name)[1].lower()
        super().save(*args, **kwargs)


class DocumentEmbedding(BaseModel):
    """문서 임베딩 모델"""
    document = models.OneToOneField(
        Document, 
        on_delete=models.CASCADE,
        related_name='embedding',
        verbose_name='문서'
    )
    embedding_vector = models.JSONField(verbose_name='임베딩 벡터')
    embedding_model = models.CharField(max_length=100, verbose_name='임베딩 모델')
    chunk_size = models.IntegerField(default=1000, verbose_name='청크 크기')

    class Meta:
        verbose_name = '문서 임베딩'
        verbose_name_plural = '문서 임베딩'

    def __str__(self):
        return f'{self.document.title} - 임베딩'


class DocumentChunk(BaseModel):
    """문서 청크 모델 (긴 문서를 나눈 조각)"""
    document = models.ForeignKey(
        Document,
        on_delete=models.CASCADE,
        related_name='chunks',
        verbose_name='문서'
    )
    content = models.TextField(verbose_name='청크 내용')
    chunk_index = models.PositiveIntegerField(verbose_name='청크 순서')
    embedding_vector = models.JSONField(null=True, blank=True, verbose_name='임베딩 벡터')

    class Meta:
        verbose_name = '문서 청크'
        verbose_name_plural = '문서 청크'
        ordering = ['document', 'chunk_index']
        unique_together = ['document', 'chunk_index']

    def __str__(self):
        return f'{self.document.title} - 청크 {self.chunk_index}'


class EmbeddingCache(BaseModel):
    """임베딩 캐시 모델 - 중복 임베딩 방지"""

    # 캐시 키 (텍스트 해시)
    content_hash = models.CharField(
        max_length=64,
        unique=True,
        verbose_name='내용 해시',
        help_text='텍스트 내용의 SHA-256 해시'
    )

    # 원본 텍스트 (디버깅용)
    original_text = models.TextField(verbose_name='원본 텍스트')

    # 임베딩 결과
    embedding_vector = models.JSONField(verbose_name='임베딩 벡터')
    embedding_model = models.CharField(
        max_length=100,
        verbose_name='임베딩 모델',
        help_text='사용된 임베딩 모델 (예: models/embedding-001)'
    )
    vector_dimension = models.IntegerField(verbose_name='벡터 차원')

    # 메타데이터
    source_type = models.CharField(
        max_length=50,
        verbose_name='소스 타입',
        help_text='notion, document, web 등'
    )
    source_id = models.CharField(
        max_length=200,
        blank=True,
        verbose_name='소스 ID',
        help_text='노션 페이지 ID, 문서 ID 등'
    )

    # 사용 통계
    hit_count = models.PositiveIntegerField(
        default=0,
        verbose_name='캐시 히트 수'
    )
    last_accessed = models.DateTimeField(
        auto_now=True,
        verbose_name='마지막 접근'
    )

    class Meta:
        verbose_name = '임베딩 캐시'
        verbose_name_plural = '임베딩 캐시'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['content_hash']),
            models.Index(fields=['embedding_model']),
            models.Index(fields=['source_type', 'source_id']),
        ]

    def __str__(self):
        return f'캐시: {self.content_hash[:8]}... ({self.embedding_model})'

    @classmethod
    def generate_content_hash(cls, text: str, model: str = '') -> str:
        """텍스트와 모델에서 해시 생성"""
        content = f"{text}|{model}"
        return hashlib.sha256(content.encode('utf-8')).hexdigest()

    @classmethod
    def get_or_create_embedding(cls, text: str, embedding_model: str,
                               source_type: str = '', source_id: str = ''):
        """캐시에서 임베딩을 가져오거나 새로 생성"""
        content_hash = cls.generate_content_hash(text, embedding_model)

        try:
            # 캐시에서 찾기
            cache_entry = cls.objects.get(
                content_hash=content_hash,
                embedding_model=embedding_model
            )
            # 히트 카운트 증가
            cache_entry.hit_count += 1
            cache_entry.save(update_fields=['hit_count', 'last_accessed'])

            return cache_entry.embedding_vector, True  # (벡터, 캐시_히트)

        except cls.DoesNotExist:
            # 캐시에 없음 - 새로 생성 필요
            return None, False

    def save_embedding(self, text: str, embedding_vector: list,
                      embedding_model: str, source_type: str = '',
                      source_id: str = ''):
        """새 임베딩을 캐시에 저장"""
        self.content_hash = self.generate_content_hash(text, embedding_model)
        self.original_text = text[:1000]  # 처음 1000자만 저장
        self.embedding_vector = embedding_vector
        self.embedding_model = embedding_model
        self.vector_dimension = len(embedding_vector)
        self.source_type = source_type
        self.source_id = source_id
        self.hit_count = 0
        self.save()
