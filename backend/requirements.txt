# Django 및 REST Framework
Django==4.2.7
djangorestframework==3.14.0
django-cors-headers==4.3.1
django-filter==23.3

# 데이터베이스
psycopg2-binary==2.9.7
dj-database-url==2.1.0

# Celery 및 Redis
celery==5.3.4
redis==5.0.1
flower==2.0.1

# 파일 처리
Pillow==10.1.0
PyPDF2==3.0.1
python-docx==1.1.0
openpyxl==3.1.2

# AI/ML 라이브러리 (CPU 최적화)
numpy<2.0.0
openai==1.54.3
google-generativeai==0.3.1
sentence-transformers==2.7.0
faiss-cpu==1.7.4
qdrant-client==1.6.9
torch==2.1.0
huggingface-hub==0.20.3

# 유틸리티
python-decouple==3.8
requests==2.31.0
beautifulsoup4==4.12.2
pytesseract==0.3.10

# 외부 서비스 연동
notion-client==2.2.1
google-api-python-client==2.108.0
google-auth-httplib2==0.1.1
google-auth-oauthlib==1.1.0

# 개발 도구
django-extensions==3.2.3
ipython==8.17.2
openai==1.97.0
