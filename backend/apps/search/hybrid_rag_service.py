"""
하이브리드 RAG 서비스 - Gemini + OpenAI 통합 검색 및 생성
"""
import os
import logging
from typing import List, Dict, Optional, Tuple
import google.generativeai as genai
from openai import OpenAI
from apps.documents.embedding_service import embedding_service
from .faiss_service import faiss_service

logger = logging.getLogger(__name__)


class HybridRAGService:
    """하이브리드 RAG 시스템 - 두 모델의 장점을 결합"""
    
    def __init__(self):
        # API 클라이언트 설정
        self.setup_apis()
        
        # 검색 가중치 설정
        self.gemini_weight = 0.4  # 빠른 검색
        self.openai_weight = 0.6  # 정밀한 검색
    
    def setup_apis(self):
        """API 클라이언트 설정"""
        # Gemini API
        gemini_key = os.getenv('GEMINI_API_KEY')
        if gemini_key:
            genai.configure(api_key=gemini_key)
            self.gemini_available = True
        else:
            self.gemini_available = False
            logger.warning("Gemini API 키가 없습니다.")
        
        # OpenAI API
        openai_key = os.getenv('OPENAI_API_KEY')
        if openai_key:
            self.openai_client = OpenAI(api_key=openai_key)
            self.openai_available = True
        else:
            self.openai_client = None
            self.openai_available = False
            logger.warning("OpenAI API 키가 없습니다.")
    
    def search(self, query: str, k: int = 5, strategy: str = 'hybrid') -> Dict:
        """
        하이브리드 검색 실행
        
        Args:
            query: 검색 쿼리
            k: 반환할 결과 수
            strategy: 검색 전략 ('hybrid', 'gemini', 'openai', 'ensemble')
            
        Returns:
            검색 결과 딕셔너리
        """
        logger.info(f"하이브리드 검색 시작: '{query}' (전략: {strategy})")
        
        if strategy == 'hybrid':
            return self._hybrid_search(query, k)
        elif strategy == 'gemini':
            return self._gemini_search(query, k)
        elif strategy == 'openai':
            return self._openai_search(query, k)
        elif strategy == 'ensemble':
            return self._ensemble_search(query, k)
        else:
            raise ValueError(f"지원하지 않는 검색 전략: {strategy}")
    
    def _hybrid_search(self, query: str, k: int) -> Dict:
        """하이브리드 검색 - 두 모델 결과를 가중 평균으로 결합"""
        results = {
            'strategy': 'hybrid',
            'query': query,
            'gemini_results': [],
            'openai_results': [],
            'combined_results': [],
            'stats': {}
        }
        
        # 1. 쿼리 임베딩 생성 (두 모델)
        gemini_embedding = None
        openai_embedding = None
        
        if self.gemini_available:
            gemini_embedding = embedding_service.generate_embedding(
                text=query,
                model='models/embedding-001',
                source_type='query',
                source_id='search_query'
            )
        
        if self.openai_available:
            openai_embedding = embedding_service.generate_embedding(
                text=query,
                model='text-embedding-3-small',
                source_type='query',
                source_id='search_query'
            )
        
        # 2. 각 인덱스에서 검색
        if gemini_embedding:
            gemini_results = faiss_service.search_gemini(gemini_embedding, k)
            results['gemini_results'] = gemini_results
        
        if openai_embedding:
            openai_results = faiss_service.search_openai(openai_embedding, k)
            results['openai_results'] = openai_results
        
        # 3. 결과 결합 (가중 평균)
        combined_results = self._combine_results(
            results['gemini_results'],
            results['openai_results'],
            k
        )
        results['combined_results'] = combined_results
        
        # 4. 통계 정보
        results['stats'] = {
            'gemini_count': len(results['gemini_results']),
            'openai_count': len(results['openai_results']),
            'combined_count': len(combined_results),
            'gemini_weight': self.gemini_weight,
            'openai_weight': self.openai_weight
        }
        
        return results
    
    def _gemini_search(self, query: str, k: int) -> Dict:
        """Gemini 전용 검색"""
        results = {
            'strategy': 'gemini',
            'query': query,
            'results': [],
            'stats': {}
        }
        
        if not self.gemini_available:
            logger.error("Gemini API를 사용할 수 없습니다.")
            return results
        
        # 쿼리 임베딩 생성
        embedding = embedding_service.generate_embedding(
            text=query,
            model='models/embedding-001',
            source_type='query',
            source_id='gemini_search'
        )
        
        if embedding:
            search_results = faiss_service.search_gemini(embedding, k)
            results['results'] = search_results
            results['stats'] = {
                'model': 'models/embedding-001',
                'dimension': 768,
                'result_count': len(search_results)
            }
        
        return results
    
    def _openai_search(self, query: str, k: int) -> Dict:
        """OpenAI 전용 검색"""
        results = {
            'strategy': 'openai',
            'query': query,
            'results': [],
            'stats': {}
        }
        
        if not self.openai_available:
            logger.error("OpenAI API를 사용할 수 없습니다.")
            return results
        
        # 쿼리 임베딩 생성
        embedding = embedding_service.generate_embedding(
            text=query,
            model='text-embedding-3-small',
            source_type='query',
            source_id='openai_search'
        )
        
        if embedding:
            search_results = faiss_service.search_openai(embedding, k)
            results['results'] = search_results
            results['stats'] = {
                'model': 'text-embedding-3-small',
                'dimension': 1536,
                'result_count': len(search_results)
            }
        
        return results
    
    def _ensemble_search(self, query: str, k: int) -> Dict:
        """앙상블 검색 - 두 모델 결과를 독립적으로 표시"""
        gemini_results = self._gemini_search(query, k)
        openai_results = self._openai_search(query, k)
        
        return {
            'strategy': 'ensemble',
            'query': query,
            'gemini': gemini_results,
            'openai': openai_results,
            'stats': {
                'gemini_count': len(gemini_results.get('results', [])),
                'openai_count': len(openai_results.get('results', []))
            }
        }
    
    def _combine_results(self, gemini_results: List[Dict], openai_results: List[Dict], k: int) -> List[Dict]:
        """두 모델의 검색 결과를 결합"""
        # 결과를 source_id 기준으로 그룹화
        result_map = {}
        
        # Gemini 결과 추가
        for result in gemini_results:
            source_id = result.get('source_id', '')
            if source_id:
                result_map[source_id] = {
                    'source_id': source_id,
                    'text': result.get('text', ''),
                    'source_type': result.get('source_type', ''),
                    'gemini_distance': result.get('distance', 999.0),
                    'gemini_rank': result.get('rank', 999),
                    'openai_distance': 999.0,
                    'openai_rank': 999,
                    'combined_score': 0.0
                }
        
        # OpenAI 결과 추가/업데이트
        for result in openai_results:
            source_id = result.get('source_id', '')
            if source_id:
                if source_id in result_map:
                    result_map[source_id]['openai_distance'] = result.get('distance', 999.0)
                    result_map[source_id]['openai_rank'] = result.get('rank', 999)
                else:
                    result_map[source_id] = {
                        'source_id': source_id,
                        'text': result.get('text', ''),
                        'source_type': result.get('source_type', ''),
                        'gemini_distance': 999.0,
                        'gemini_rank': 999,
                        'openai_distance': result.get('distance', 999.0),
                        'openai_rank': result.get('rank', 999),
                        'combined_score': 0.0
                    }
        
        # 결합 점수 계산 (거리가 작을수록 좋음)
        for item in result_map.values():
            # 정규화된 점수 계산 (0~1, 낮을수록 좋음)
            gemini_score = 1.0 / (1.0 + item['gemini_distance']) if item['gemini_distance'] < 999.0 else 0.0
            openai_score = 1.0 / (1.0 + item['openai_distance']) if item['openai_distance'] < 999.0 else 0.0
            
            # 가중 평균
            item['combined_score'] = (
                self.gemini_weight * gemini_score + 
                self.openai_weight * openai_score
            )
        
        # 결합 점수로 정렬 (높은 점수가 더 좋음)
        combined_results = sorted(
            result_map.values(),
            key=lambda x: x['combined_score'],
            reverse=True
        )
        
        return combined_results[:k]
    
    def generate_answer(self, query: str, search_results: List[Dict], model: str = 'gemini') -> str:
        """
        검색 결과를 바탕으로 답변 생성
        
        Args:
            query: 사용자 질문
            search_results: 검색 결과
            model: 사용할 생성 모델 ('gemini' 또는 'openai')
            
        Returns:
            생성된 답변
        """
        if not search_results:
            return "죄송합니다. 관련된 정보를 찾을 수 없습니다."
        
        # 컨텍스트 구성
        context_parts = []
        for i, result in enumerate(search_results[:3], 1):  # 상위 3개 결과만 사용
            text = result.get('text', '').strip()
            if text:
                context_parts.append(f"{i}. {text}")
        
        context = "\n".join(context_parts)
        
        # 프롬프트 구성
        prompt = f"""당신은 위탁판매 및 농산물 마케팅 전문가입니다. 다음 정보를 바탕으로 질문에 실용적이고 구체적인 답변을 제공해주세요.

질문: {query}

관련 정보:
{context}

답변 지침:
- 제공된 정보를 적극적으로 활용하여 실용적인 답변을 제공하세요
- 위탁판매, 브랜딩, 마케팅 관점에서 구체적인 조언을 해주세요
- 정보에서 유추할 수 있는 내용도 포함하여 도움이 되는 답변을 만드세요
- 단순히 "정보가 부족하다"고 하지 말고, 있는 정보로 최대한 도움을 주세요
- 실무에 바로 적용할 수 있는 구체적인 방법을 제시해주세요
- 한국어로 친근하고 전문적인 톤으로 답변해주세요

답변:"""
        
        # 모델별 답변 생성
        if model == 'gemini' and self.gemini_available:
            return self._generate_with_gemini(prompt)
        elif model == 'openai' and self.openai_available:
            return self._generate_with_openai(prompt)
        else:
            return "죄송합니다. 답변 생성 서비스를 사용할 수 없습니다."
    
    def _generate_with_gemini(self, prompt: str) -> str:
        """Gemini로 답변 생성"""
        try:
            model = genai.GenerativeModel('gemini-1.5-flash')
            response = model.generate_content(prompt)
            return response.text
        except Exception as e:
            logger.error(f"Gemini 답변 생성 오류: {e}")
            return "답변 생성 중 오류가 발생했습니다."
    
    def _generate_with_openai(self, prompt: str) -> str:
        """OpenAI로 답변 생성"""
        try:
            response = self.openai_client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "user", "content": prompt}
                ],
                max_tokens=500,
                temperature=0.7
            )
            return response.choices[0].message.content
        except Exception as e:
            logger.error(f"OpenAI 답변 생성 오류: {e}")
            return "답변 생성 중 오류가 발생했습니다."
    
    def rag_query(self, query: str, k: int = 5, search_strategy: str = 'hybrid', 
                  generation_model: str = 'gemini') -> Dict:
        """
        완전한 RAG 쿼리 실행
        
        Args:
            query: 사용자 질문
            k: 검색할 결과 수
            search_strategy: 검색 전략
            generation_model: 답변 생성 모델
            
        Returns:
            RAG 결과 딕셔너리
        """
        # 1. 검색 실행
        search_results = self.search(query, k, search_strategy)
        
        # 2. 검색 결과에서 최상위 결과 추출
        if search_strategy == 'hybrid':
            top_results = search_results.get('combined_results', [])
        elif search_strategy in ['gemini', 'openai']:
            top_results = search_results.get('results', [])
        elif search_strategy == 'ensemble':
            # 앙상블의 경우 두 결과를 합쳐서 사용
            gemini_results = search_results.get('gemini', {}).get('results', [])
            openai_results = search_results.get('openai', {}).get('results', [])
            top_results = gemini_results + openai_results
        else:
            top_results = []
        
        # 3. 답변 생성
        answer = self.generate_answer(query, top_results, generation_model)
        
        return {
            'query': query,
            'answer': answer,
            'search_results': search_results,
            'search_strategy': search_strategy,
            'generation_model': generation_model,
            'context_count': len(top_results)
        }

    def _generate_response_with_context(self, query: str, context: str) -> str:
        """컨텍스트를 사용하여 응답 생성"""
        try:
            if self.gemini_available:
                # Gemini로 응답 생성
                model = genai.GenerativeModel('gemini-1.5-flash')
                prompt = f"{context}\n\n사용자 질문: {query}\n\n답변:"

                response = model.generate_content(prompt)
                return response.text

            elif self.openai_available:
                # OpenAI로 응답 생성
                response = self.openai_client.chat.completions.create(
                    model="gpt-3.5-turbo",
                    messages=[
                        {"role": "system", "content": context},
                        {"role": "user", "content": query}
                    ],
                    max_tokens=1000,
                    temperature=0.7
                )
                return response.choices[0].message.content

            else:
                return "죄송합니다. AI 서비스를 사용할 수 없습니다."

        except Exception as e:
            logger.error(f"Response generation error: {str(e)}")
            return "죄송합니다. 응답 생성 중 오류가 발생했습니다."


# 전역 인스턴스
hybrid_rag_service = HybridRAGService()
