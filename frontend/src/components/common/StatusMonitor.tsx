'use client';

import { useState, useEffect } from 'react';
import { documentsApi } from '@/lib/api';
import { CheckCircle, Clock, AlertCircle, XCircle, RefreshCw } from 'lucide-react';
import { clsx } from 'clsx';

interface TaskStatus {
  task_id: string;
  status: string;
  ready: boolean;
  successful?: boolean;
  failed?: boolean;
  result?: any;
  error?: string;
}

interface StatusMonitorProps {
  taskId: string;
  onComplete?: (result: any) => void;
  onError?: (error: string) => void;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

export function StatusMonitor({ 
  taskId, 
  onComplete, 
  onError, 
  autoRefresh = true, 
  refreshInterval = 2000 
}: StatusMonitorProps) {
  const [status, setStatus] = useState<TaskStatus | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchStatus = async () => {
    try {
      const response = await documentsApi.getTaskStatus(taskId);
      setStatus(response);
      setError(null);

      if (response.ready) {
        if (response.successful && onComplete) {
          onComplete(response.result);
        } else if (response.failed && onError) {
          onError(response.error || '작업이 실패했습니다.');
        }
      }
    } catch (err) {
      console.error('Status fetch error:', err);
      setError('상태 조회 중 오류가 발생했습니다.');
      if (onError) {
        onError('상태 조회 실패');
      }
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchStatus();

    if (autoRefresh) {
      const interval = setInterval(() => {
        if (status?.ready) {
          clearInterval(interval);
          return;
        }
        fetchStatus();
      }, refreshInterval);

      return () => clearInterval(interval);
    }
  }, [taskId, autoRefresh, refreshInterval, status?.ready]);

  const getStatusIcon = () => {
    if (isLoading) return <RefreshCw className="h-5 w-5 animate-spin text-blue-500" />;
    if (error) return <XCircle className="h-5 w-5 text-red-500" />;
    if (!status) return <Clock className="h-5 w-5 text-gray-400" />;

    if (status.ready) {
      if (status.successful) {
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      } else {
        return <XCircle className="h-5 w-5 text-red-500" />;
      }
    }

    return <Clock className="h-5 w-5 text-yellow-500" />;
  };

  const getStatusText = () => {
    if (isLoading) return '상태 확인 중...';
    if (error) return error;
    if (!status) return '상태 정보 없음';

    if (status.ready) {
      if (status.successful) {
        return '완료됨';
      } else {
        return `실패: ${status.error || '알 수 없는 오류'}`;
      }
    }

    return '처리 중...';
  };

  const getStatusColor = () => {
    if (isLoading) return 'text-blue-600';
    if (error) return 'text-red-600';
    if (!status) return 'text-gray-600';

    if (status.ready) {
      if (status.successful) {
        return 'text-green-600';
      } else {
        return 'text-red-600';
      }
    }

    return 'text-yellow-600';
  };

  return (
    <div className="flex items-center space-x-2">
      {getStatusIcon()}
      <span className={clsx('text-sm font-medium', getStatusColor())}>
        {getStatusText()}
      </span>
      {status?.ready && status.successful && status.result && (
        <div className="ml-4 text-xs text-gray-500">
          {status.result.document_id && (
            <span>문서 ID: {status.result.document_id}</span>
          )}
        </div>
      )}
    </div>
  );
}

// 여러 태스크를 모니터링하는 컴포넌트
interface MultiTaskMonitorProps {
  tasks: Array<{
    id: string;
    taskId: string;
    name: string;
  }>;
  onTaskComplete?: (taskId: string, result: any) => void;
  onTaskError?: (taskId: string, error: string) => void;
}

export function MultiTaskMonitor({ tasks, onTaskComplete, onTaskError }: MultiTaskMonitorProps) {
  return (
    <div className="space-y-3">
      <h3 className="text-sm font-medium text-gray-900">작업 진행 상황</h3>
      <div className="space-y-2">
        {tasks.map((task) => (
          <div key={task.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <span className="text-sm text-gray-700">{task.name}</span>
            <StatusMonitor
              taskId={task.taskId}
              onComplete={(result) => onTaskComplete?.(task.taskId, result)}
              onError={(error) => onTaskError?.(task.taskId, error)}
            />
          </div>
        ))}
      </div>
    </div>
  );
}

// 임시 저장소 상태 모니터
interface TempStorageMonitorProps {
  refreshInterval?: number;
}

export function TempStorageMonitor({ refreshInterval = 5000 }: TempStorageMonitorProps) {
  const [storageInfo, setStorageInfo] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);

  const fetchStorageInfo = async () => {
    try {
      const response = await documentsApi.getTempList();
      setStorageInfo(response.storage_info);
    } catch (err) {
      console.error('Storage info fetch error:', err);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchStorageInfo();
    const interval = setInterval(fetchStorageInfo, refreshInterval);
    return () => clearInterval(interval);
  }, [refreshInterval]);

  if (isLoading) {
    return (
      <div className="flex items-center space-x-2">
        <RefreshCw className="h-4 w-4 animate-spin text-blue-500" />
        <span className="text-sm text-gray-600">저장소 정보 로딩 중...</span>
      </div>
    );
  }

  if (!storageInfo) return null;

  return (
    <div className="bg-white border rounded-lg p-4">
      <h3 className="text-sm font-medium text-gray-900 mb-3">임시 저장소 상태</h3>
      <div className="grid grid-cols-2 gap-4 text-sm">
        <div>
          <span className="text-gray-600">총 파일:</span>
          <span className="ml-2 font-medium">{storageInfo.total_files}개</span>
        </div>
        <div>
          <span className="text-gray-600">사용량:</span>
          <span className="ml-2 font-medium">{storageInfo.total_size_mb}MB</span>
        </div>
        <div>
          <span className="text-gray-600">처리 대기:</span>
          <span className="ml-2 font-medium text-yellow-600">{storageInfo.by_status.pending}개</span>
        </div>
        <div>
          <span className="text-gray-600">처리 중:</span>
          <span className="ml-2 font-medium text-blue-600">{storageInfo.by_status.processing}개</span>
        </div>
        <div>
          <span className="text-gray-600">완료:</span>
          <span className="ml-2 font-medium text-green-600">{storageInfo.by_status.completed}개</span>
        </div>
        <div>
          <span className="text-gray-600">실패:</span>
          <span className="ml-2 font-medium text-red-600">{storageInfo.by_status.failed}개</span>
        </div>
      </div>
      <div className="mt-3 bg-gray-200 rounded-full h-2">
        <div 
          className="bg-blue-600 h-2 rounded-full transition-all duration-300"
          style={{ width: `${Math.min(storageInfo.usage_percent, 100)}%` }}
        />
      </div>
      <div className="mt-1 text-xs text-gray-500 text-center">
        {storageInfo.usage_percent.toFixed(1)}% 사용 중 (최대 {storageInfo.max_size_mb}MB)
      </div>
    </div>
  );
}
