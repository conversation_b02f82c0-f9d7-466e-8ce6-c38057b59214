"""
문서 청크 분할 서비스
- 다양한 문서 타입별 청크 분할
- 의미 단위 기반 분할
- 청크 크기 최적화
- 중복 제거
"""
import re
import logging
from typing import List, Dict, Any, Optional, Tuple
from django.conf import settings
from .models import Document, DocumentChunk
from .embedding_service import embedding_service

logger = logging.getLogger(__name__)


class ChunkService:
    """문서 청크 분할 서비스"""
    
    def __init__(self):
        self.default_chunk_size = 1000
        self.chunk_overlap = 200
        self.min_chunk_size = 100
        self.max_chunk_size = 2000
        
    def create_chunks(self, document: Document, force_recreate: bool = False) -> List[DocumentChunk]:
        """
        문서의 청크 생성
        
        Args:
            document: 청크를 생성할 문서
            force_recreate: 기존 청크를 강제로 재생성할지 여부
            
        Returns:
            생성된 청크 리스트
        """
        try:
            # 기존 청크 확인
            if not force_recreate and document.chunks.exists():
                logger.info(f"문서 {document.id}의 기존 청크 사용")
                return list(document.chunks.all().order_by('chunk_index'))
            
            # 기존 청크 삭제 (재생성인 경우)
            if force_recreate:
                document.chunks.all().delete()
                logger.info(f"문서 {document.id}의 기존 청크 삭제")
            
            # 문서 내용 추출
            content = self._extract_document_content(document)
            if not content.strip():
                logger.warning(f"문서 {document.id}에서 추출할 내용이 없습니다.")
                return []
            
            # 문서 타입별 청크 분할
            chunks_text = self._split_by_document_type(content, document.document_type)
            
            # 청크 객체 생성
            chunks = []
            for i, chunk_text in enumerate(chunks_text):
                if len(chunk_text.strip()) >= self.min_chunk_size:
                    chunk = DocumentChunk.objects.create(
                        document=document,
                        content=chunk_text.strip(),
                        chunk_index=i
                    )
                    chunks.append(chunk)
            
            logger.info(f"문서 {document.id}의 청크 생성 완료: {len(chunks)}개")
            return chunks
            
        except Exception as e:
            logger.error(f"문서 {document.id} 청크 생성 실패: {str(e)}")
            return []
    
    def _extract_document_content(self, document: Document) -> str:
        """문서에서 텍스트 내용 추출"""
        content_parts = []
        
        # 제목과 설명 추가
        if document.title:
            content_parts.append(f"제목: {document.title}")
        
        if document.description:
            content_parts.append(f"설명: {document.description}")
        
        # 추출된 텍스트 우선 사용
        if document.extracted_text:
            content_parts.append(document.extracted_text)
        elif document.content:
            content_parts.append(document.content)
        
        return "\n\n".join(content_parts)
    
    def _split_by_document_type(self, content: str, document_type: str) -> List[str]:
        """문서 타입별 청크 분할"""
        if document_type == 'pdf':
            return self._split_pdf_content(content)
        elif document_type == 'text':
            return self._split_text_content(content)
        elif document_type == 'web':
            return self._split_web_content(content)
        elif document_type == 'chat':
            return self._split_chat_content(content)
        else:
            return self._split_generic_content(content)
    
    def _split_pdf_content(self, content: str) -> List[str]:
        """PDF 내용 분할 - 페이지, 섹션 기준"""
        # 페이지 구분자로 먼저 분할
        pages = re.split(r'\n\s*(?:페이지|Page)\s*\d+\s*\n', content)
        
        chunks = []
        for page in pages:
            if len(page.strip()) > self.max_chunk_size:
                # 큰 페이지는 문단으로 재분할
                page_chunks = self._split_by_paragraphs(page)
                chunks.extend(page_chunks)
            else:
                chunks.append(page.strip())
        
        return [chunk for chunk in chunks if len(chunk.strip()) >= self.min_chunk_size]
    
    def _split_text_content(self, content: str) -> List[str]:
        """텍스트 내용 분할 - 문단 기준"""
        return self._split_by_paragraphs(content)
    
    def _split_web_content(self, content: str) -> List[str]:
        """웹 내용 분할 - HTML 구조 고려"""
        # HTML 태그 제거 후 문단 분할
        clean_content = re.sub(r'<[^>]+>', '', content)
        return self._split_by_paragraphs(clean_content)
    
    def _split_chat_content(self, content: str) -> List[str]:
        """채팅 내용 분할 - 대화 단위"""
        # 대화 패턴 인식 (시간, 사용자명 등)
        chat_pattern = r'(\d{4}-\d{2}-\d{2}|\d{2}:\d{2}|\[.*?\])'
        
        # 대화 단위로 분할
        messages = re.split(chat_pattern, content)
        
        chunks = []
        current_chunk = ""
        
        for message in messages:
            if len(current_chunk + message) > self.default_chunk_size:
                if current_chunk.strip():
                    chunks.append(current_chunk.strip())
                current_chunk = message
            else:
                current_chunk += message
        
        if current_chunk.strip():
            chunks.append(current_chunk.strip())
        
        return [chunk for chunk in chunks if len(chunk.strip()) >= self.min_chunk_size]
    
    def _split_generic_content(self, content: str) -> List[str]:
        """일반적인 내용 분할"""
        return self._split_by_paragraphs(content)
    
    def _split_by_paragraphs(self, content: str) -> List[str]:
        """문단 기준 분할"""
        # 문단 구분자로 분할
        paragraphs = re.split(r'\n\s*\n', content)
        
        chunks = []
        current_chunk = ""
        
        for paragraph in paragraphs:
            paragraph = paragraph.strip()
            if not paragraph:
                continue
            
            # 현재 청크에 추가했을 때 크기 확인
            potential_chunk = current_chunk + "\n\n" + paragraph if current_chunk else paragraph
            
            if len(potential_chunk) > self.default_chunk_size:
                # 현재 청크 저장
                if current_chunk:
                    chunks.append(current_chunk.strip())
                
                # 문단이 너무 큰 경우 문장으로 재분할
                if len(paragraph) > self.max_chunk_size:
                    sentence_chunks = self._split_by_sentences(paragraph)
                    chunks.extend(sentence_chunks[:-1])  # 마지막 제외
                    current_chunk = sentence_chunks[-1] if sentence_chunks else ""
                else:
                    current_chunk = paragraph
            else:
                current_chunk = potential_chunk
        
        # 마지막 청크 추가
        if current_chunk.strip():
            chunks.append(current_chunk.strip())
        
        return [chunk for chunk in chunks if len(chunk.strip()) >= self.min_chunk_size]
    
    def _split_by_sentences(self, text: str) -> List[str]:
        """문장 기준 분할"""
        # 한국어 문장 구분자
        sentences = re.split(r'[.!?]\s+', text)
        
        chunks = []
        current_chunk = ""
        
        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue
            
            potential_chunk = current_chunk + ". " + sentence if current_chunk else sentence
            
            if len(potential_chunk) > self.default_chunk_size:
                if current_chunk:
                    chunks.append(current_chunk.strip())
                current_chunk = sentence
            else:
                current_chunk = potential_chunk
        
        if current_chunk.strip():
            chunks.append(current_chunk.strip())
        
        return chunks
    
    def generate_chunk_embeddings(self, chunks: List[DocumentChunk], 
                                 model: str = 'models/embedding-001') -> int:
        """
        청크들의 임베딩 생성
        
        Args:
            chunks: 임베딩을 생성할 청크 리스트
            model: 사용할 임베딩 모델
            
        Returns:
            성공적으로 임베딩이 생성된 청크 수
        """
        success_count = 0
        
        for chunk in chunks:
            try:
                # 이미 임베딩이 있는 경우 건너뛰기
                if chunk.embedding_vector:
                    continue
                
                # 임베딩 생성
                embedding = embedding_service.generate_embedding(
                    text=chunk.content,
                    model=model,
                    source_type='document_chunk',
                    source_id=str(chunk.id)
                )
                
                if embedding:
                    chunk.embedding_vector = embedding
                    chunk.save(update_fields=['embedding_vector'])
                    success_count += 1
                    logger.debug(f"청크 {chunk.id} 임베딩 생성 완료")
                
            except Exception as e:
                logger.error(f"청크 {chunk.id} 임베딩 생성 실패: {str(e)}")
        
        logger.info(f"청크 임베딩 생성 완료: {success_count}/{len(chunks)}")
        return success_count
    
    def get_chunk_stats(self, document: Document = None) -> Dict[str, Any]:
        """청크 통계 정보"""
        try:
            queryset = DocumentChunk.objects.all()
            if document:
                queryset = queryset.filter(document=document)
            
            total_chunks = queryset.count()
            embedded_chunks = queryset.filter(embedding_vector__isnull=False).count()
            
            # 청크 크기 통계
            chunk_sizes = [len(chunk.content) for chunk in queryset.iterator()]
            
            stats = {
                'total_chunks': total_chunks,
                'embedded_chunks': embedded_chunks,
                'embedding_coverage': round((embedded_chunks / total_chunks * 100), 2) if total_chunks > 0 else 0,
                'chunk_size_stats': {
                    'min': min(chunk_sizes) if chunk_sizes else 0,
                    'max': max(chunk_sizes) if chunk_sizes else 0,
                    'avg': round(sum(chunk_sizes) / len(chunk_sizes), 2) if chunk_sizes else 0
                }
            }
            
            if document:
                stats['document_id'] = document.id
                stats['document_title'] = document.title
            
            return stats
            
        except Exception as e:
            logger.error(f"청크 통계 조회 실패: {str(e)}")
            return {}


# 전역 청크 서비스 인스턴스
chunk_service = ChunkService()


def get_chunk_service() -> ChunkService:
    """청크 서비스 인스턴스 반환"""
    return chunk_service
