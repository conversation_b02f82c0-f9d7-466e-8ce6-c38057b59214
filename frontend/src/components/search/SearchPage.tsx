'use client';

import { useState } from 'react';
import {
  Search,
  FileText,
  Image,
  Video,
  File,
  Clock,
  Filter,
  X
} from 'lucide-react';
import { searchApi } from '@/lib/api';

interface SearchResult {
  id: number;
  title: string;
  content: string;
  document_type: string;
  score: number;
  highlights: string[];
  category: string;
  created_at: string;
}

const mockSearchResults: SearchResult[] = [
  {
    id: 1,
    title: '위탁판매 기초 가이드.pdf',
    document_type: 'pdf',
    content: '위탁판매는 상품을 직접 구매하지 않고 판매자가 상품을 보관하고 판매하는 방식입니다...',
    category: '강의 자료',
    created_at: '2024-01-15T10:30:00Z',
    score: 0.95,
    highlights: ['위탁판매', '기초', '가이드'],
  },
  {
    id: 2,
    title: '시장 분석 자료.pdf',
    document_type: 'pdf',
    content: '현재 위탁판매 시장의 트렌드와 성장 가능성에 대한 분석 자료입니다...',
    category: '분석 자료',
    created_at: '2024-01-13T16:45:00Z',
    score: 0.87,
    highlights: ['시장', '분석', '트렌드'],
  },
  {
    id: 3,
    title: '주부의 식탁 위탁판매 가이드.pdf',
    document_type: 'pdf',
    content: '주부의 식탁은 대한민국 대표 식품 위탁판매 플랫폼입니다. 이 가이드에서는 주부의 식탁에서 성공적인 위탁판매를 위한 전략과 방법을 소개합니다...',
    category: '강의 자료',
    created_at: '2024-01-12T09:20:00Z',
    score: 0.98,
    highlights: ['주부의 식탁', '위탁판매', '식품', '플랫폼'],
  },
  {
    id: 4,
    title: '주부의 식탁 상품 등록 방법.png',
    document_type: 'image',
    content: '주부의 식탁 플랫폼에서 상품을 등록하는 단계별 방법을 스크린샷으로 정리한 자료입니다...',
    category: '스크린샷',
    created_at: '2024-01-11T15:30:00Z',
    score: 0.92,
    highlights: ['주부의 식탁', '상품등록', '방법'],
  },
  {
    id: 5,
    title: '주부의 식탁 판매자 교육 영상.mp4',
    document_type: 'video',
    content: '주부의 식탁에서 제공하는 판매자를 위한 교육 영상입니다. 성공적인 판매 전략과 고객 응대 방법을 다룹니다...',
    category: '비디오',
    created_at: '2024-01-10T11:45:00Z',
    score: 0.89,
    highlights: ['주부의 식탁', '교육', '판매자', '영상'],
  },
  {
    id: 6,
    title: '주부의 식탁 수수료 정책.png',
    document_type: 'image',
    content: '주부의 식탁의 수수료 정책과 정산 방법에 대한 상세한 안내 자료입니다...',
    category: '정책 자료',
    created_at: '2024-01-07T10:10:00Z',
    score: 0.85,
    highlights: ['주부의 식탁', '수수료', '정책', '정산'],
  },
];

export function SearchPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [hasSearched, setHasSearched] = useState(false);
  const [selectedFilters, setSelectedFilters] = useState<string[]>([]);

  const handleSearch = async () => {
    if (!searchQuery.trim()) return;

    setIsSearching(true);
    setHasSearched(true);

    try {
      const response = await searchApi.hybridSearch(searchQuery, {
        top_k: 10,
        alpha: 0.5,
      });
      setSearchResults(response.results || []);
    } catch (error) {
      console.error('Search error:', error);
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  const getFileIcon = (type: string) => {
    switch (type) {
      case 'pdf':
        return FileText;
      case 'image':
        return Image;
      case 'video':
        return Video;
      case 'note':
        return FileText;
      case 'reference':
        return File;
      default:
        return File;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('ko-KR', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const highlightText = (text: string, query: string) => {
    if (!query) return text;
    
    const regex = new RegExp(`(${query})`, 'gi');
    const parts = text.split(regex);
    
    return parts.map((part, index) => 
      regex.test(part) ? (
        <mark key={index} className="bg-yellow-200 px-1 rounded">
          {part}
        </mark>
      ) : (
        part
      )
    );
  };

  const recentSearches = ['위탁판매', '시장 분석', '강의 자료', '카카오톡'];
  const popularSearches = ['기초 가이드', '트렌드 분석', '스크린샷', '녹화 강의'];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">검색</h1>
        <p className="mt-1 text-sm text-gray-600">
          업로드된 모든 문서에서 원하는 내용을 찾아보세요.
        </p>
      </div>

      {/* Search Bar */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex space-x-4">
          <div className="flex-1 relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="문서 내용, 제목, 태그로 검색..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyPress={handleKeyPress}
              className="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 text-gray-900 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-lg"
            />
          </div>
          <button
            onClick={handleSearch}
            disabled={!searchQuery.trim() || isSearching}
            className="px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSearching ? '검색 중...' : '검색'}
          </button>
        </div>

        {/* Filters */}
        <div className="mt-4 flex items-center space-x-4">
          <Filter className="h-4 w-4 text-gray-400" />
          <div className="flex flex-wrap gap-2">
            {['PDF', '이미지', '비디오', '문서'].map(filter => (
              <button
                key={filter}
                onClick={() => {
                  setSelectedFilters(prev => 
                    prev.includes(filter) 
                      ? prev.filter(f => f !== filter)
                      : [...prev, filter]
                  );
                }}
                className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                  selectedFilters.includes(filter)
                    ? 'bg-blue-100 text-blue-800'
                    : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
                }`}
              >
                {filter}
                {selectedFilters.includes(filter) && (
                  <X className="ml-1 h-3 w-3" />
                )}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Search Suggestions */}
      {!hasSearched && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-white shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">최근 검색</h3>
            <div className="space-y-2">
              {recentSearches.map((search, index) => (
                <button
                  key={index}
                  onClick={() => setSearchQuery(search)}
                  className="flex items-center w-full text-left p-2 rounded-md hover:bg-gray-50"
                >
                  <Clock className="h-4 w-4 text-gray-400 mr-3" />
                  <span className="text-sm text-gray-700">{search}</span>
                </button>
              ))}
            </div>
          </div>

          <div className="bg-white shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">인기 검색어</h3>
            <div className="space-y-2">
              {popularSearches.map((search, index) => (
                <button
                  key={index}
                  onClick={() => setSearchQuery(search)}
                  className="flex items-center w-full text-left p-2 rounded-md hover:bg-gray-50"
                >
                  <Search className="h-4 w-4 text-gray-400 mr-3" />
                  <span className="text-sm text-gray-700">{search}</span>
                </button>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Search Results */}
      {hasSearched && (
        <div className="space-y-4">
          {/* Results Header */}
          <div className="flex items-center justify-between">
            <p className="text-sm text-gray-700">
              {isSearching ? (
                '검색 중...'
              ) : (
                <>
                  "<span className="font-medium">{searchQuery}</span>"에 대한 
                  <span className="font-medium"> {searchResults.length}</span>개의 결과
                </>
              )}
            </p>
          </div>

          {/* Results List */}
          {isSearching ? (
            <div className="bg-white shadow rounded-lg p-8 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-4 text-gray-500">검색 중입니다...</p>
            </div>
          ) : searchResults.length > 0 ? (
            <div className="space-y-4">
              {searchResults.map((result) => {
                const Icon = getFileIcon(result.document_type);
                return (
                  <div key={result.id} className="bg-white shadow rounded-lg p-6 hover:shadow-md transition-shadow">
                    <div className="flex items-start space-x-4">
                      <div className="flex-shrink-0">
                        <Icon className="h-8 w-8 text-blue-500" />
                      </div>

                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between mb-2">
                          <h3 className="text-lg font-medium text-blue-600 hover:text-blue-800 cursor-pointer">
                            {highlightText(result.title, searchQuery)}
                          </h3>
                          <div className="flex items-center space-x-2">
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                              {Math.round(result.score * 100)}% 일치
                            </span>
                            <span className="text-sm text-gray-500">{formatDate(result.created_at)}</span>
                          </div>
                        </div>

                        <p className="text-sm text-gray-600 mb-3">
                          {highlightText(result.content, searchQuery)}
                        </p>

                        <div className="flex items-center space-x-4 text-sm text-gray-500">
                          <span>카테고리: {result.category}</span>
                          <div className="flex flex-wrap gap-1">
                            {result.highlights.map((highlight, index) => (
                              <span key={index} className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
                                {highlight}
                              </span>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          ) : (
            <div className="bg-white shadow rounded-lg p-8 text-center">
              <Search className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">검색 결과가 없습니다</h3>
              <p className="text-gray-500">
                다른 키워드로 검색해보시거나 필터를 조정해보세요.
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
