# Generated by Django 4.2.7 on 2025-07-20 14:31

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='EmbeddingConfig',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('chunk_size', models.IntegerField(default=1000)),
                ('chunk_overlap', models.IntegerField(default=200)),
                ('embedding_model', models.CharField(default='text-embedding-ada-002', max_length=100)),
                ('is_active', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['-is_active', '-updated_at'],
            },
        ),
        migrations.CreateModel(
            name='EmbeddingStats',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total_documents', models.IntegerField(default=0)),
                ('total_chunks', models.IntegerField(default=0)),
                ('faiss_index_size', models.IntegerField(default=0)),
                ('openai_index_size', models.IntegerField(default=0)),
                ('last_updated', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name_plural': 'Embedding Stats',
            },
        ),
    ]
