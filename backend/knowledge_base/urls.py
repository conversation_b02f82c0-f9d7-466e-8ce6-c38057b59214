from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.http import JsonResponse
from django.shortcuts import render


def home_view(request):
    """홈페이지 뷰"""
    return JsonResponse({
        'message': '지식 관리 시스템에 오신 것을 환영합니다!',
        'version': '1.0.0',
        'endpoints': {
            'admin': '/admin/',
            'api': '/api/',
            'documents': '/api/documents/',
            'search': '/api/search/',
            'integrations': '/api/integrations/',
        }
    })

urlpatterns = [
    path('admin/', admin.site.urls),
    path('api/auth/', include('apps.users.urls')),
    path('api/documents/', include('apps.documents.urls')),
    path('api/search/', include('apps.search.urls')),
    path('api/integrations/', include('apps.integrations.urls')),
    path('api/chat/', include('apps.chat.urls')),
    path('api/rag/', include('apps.rag.urls')),
    path('api/', include('apps.core.urls')),
    path('', home_view, name='home'),  # 홈 엔드포인트 추가
]

# 개발 환경에서 미디어 파일 서빙
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
