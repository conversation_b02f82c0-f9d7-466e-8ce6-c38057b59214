'use client';

import { useState } from 'react';
import { FileUploadZone } from './FileUploadZone';
import { UploadedFilesList } from './UploadedFilesList';
import { UploadProgress } from './UploadProgress';
import { documentsApi } from '@/lib/api';
import { useToast } from '@/components/common/Toast';
import { TempStorageMonitor, StatusMonitor } from '@/components/common/StatusMonitor';

export interface UploadedFile {
  id: string;
  name: string;
  size: number;
  type: string;
  status: 'uploading' | 'temp_uploaded' | 'processing' | 'completed' | 'error';
  progress: number;
  url?: string;
  error?: string;
  fileId?: string; // 임시 저장소 파일 ID
  taskId?: string; // 비동기 처리 태스크 ID
  documentId?: string; // 생성된 문서 ID
}

export function UploadPage() {
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const { success, error } = useToast();

  const handleFilesSelected = async (files: File[]) => {
    setIsUploading(true);

    const newFiles: UploadedFile[] = files.map(file => ({
      id: Math.random().toString(36).substr(2, 9),
      name: file.name,
      size: file.size,
      type: file.type,
      status: 'uploading',
      progress: 0,
    }));

    setUploadedFiles(prev => [...prev, ...newFiles]);

    // 임시 저장소에 파일 업로드
    for (const [index, file] of files.entries()) {
      const fileId = newFiles[index].id;

      try {
        // 파일 타입 결정
        let documentType = 'other';
        if (file.type.startsWith('image/')) documentType = 'image';
        else if (file.type.startsWith('video/')) documentType = 'video';
        else if (file.type === 'application/pdf') documentType = 'pdf';
        else if (file.type.startsWith('text/')) documentType = 'text';

        // 업로드 진행률 시뮬레이션
        const progressInterval = setInterval(() => {
          setUploadedFiles(prev =>
            prev.map(uploadFile =>
              uploadFile.id === fileId
                ? { ...uploadFile, progress: Math.min(uploadFile.progress + 10, 90) }
                : uploadFile
            )
          );
        }, 200);

        // 임시 저장소에 업로드
        const tempResponse = await documentsApi.tempUpload(file, {
          title: file.name,
          description: `업로드된 파일: ${file.name}`,
          document_type: documentType,
        });

        clearInterval(progressInterval);

        // 임시 업로드 완료
        setUploadedFiles(prev =>
          prev.map(uploadFile =>
            uploadFile.id === fileId
              ? {
                  ...uploadFile,
                  status: 'temp_uploaded',
                  progress: 100,
                  fileId: tempResponse.file_id
                }
              : uploadFile
          )
        );

        // 비동기 처리 시작
        const processResponse = await documentsApi.processTempFileAsync(tempResponse.file_id);

        setUploadedFiles(prev =>
          prev.map(uploadFile =>
            uploadFile.id === fileId
              ? {
                  ...uploadFile,
                  status: 'processing',
                  taskId: processResponse.task_id
                }
              : uploadFile
          )
        );

        // 태스크 상태 모니터링 시작
        monitorTaskProgress(fileId, processResponse.task_id);

        success('업로드 완료', `${file.name} 파일이 임시 저장되었습니다. 처리 중입니다.`);
      } catch (uploadError) {
        console.error('Upload error:', uploadError);
        setUploadedFiles(prev =>
          prev.map(uploadFile =>
            uploadFile.id === fileId
              ? { ...uploadFile, status: 'error', error: '업로드 실패' }
              : uploadFile
          )
        );

        error('업로드 실패', `${file.name} 파일 업로드 중 오류가 발생했습니다.`);
      }
    }

    setIsUploading(false);
  };

  // 태스크 진행 상황 모니터링
  const monitorTaskProgress = async (fileId: string, taskId: string) => {
    const checkInterval = setInterval(async () => {
      try {
        const taskStatus = await documentsApi.getTaskStatus(taskId);

        if (taskStatus.ready) {
          clearInterval(checkInterval);

          if (taskStatus.successful) {
            setUploadedFiles(prev =>
              prev.map(uploadFile =>
                uploadFile.id === fileId
                  ? {
                      ...uploadFile,
                      status: 'completed',
                      documentId: taskStatus.result?.document_id
                    }
                  : uploadFile
              )
            );
            success('처리 완료', '파일이 성공적으로 처리되었습니다.');
          } else {
            setUploadedFiles(prev =>
              prev.map(uploadFile =>
                uploadFile.id === fileId
                  ? {
                      ...uploadFile,
                      status: 'error',
                      error: taskStatus.error || '처리 실패'
                    }
                  : uploadFile
              )
            );
            error('처리 실패', '파일 처리 중 오류가 발생했습니다.');
          }
        }
      } catch (err) {
        console.error('Task monitoring error:', err);
        clearInterval(checkInterval);
      }
    }, 2000); // 2초마다 체크
  };

  const simulateFileUpload = (fileId: string, onProgress: (progress: number) => void): Promise<void> => {
    return new Promise((resolve) => {
      let progress = 0;
      const interval = setInterval(() => {
        progress += Math.random() * 20;
        if (progress >= 100) {
          progress = 100;
          clearInterval(interval);
          resolve();
        }
        onProgress(progress);
      }, 200);
    });
  };

  const handleRemoveFile = (fileId: string) => {
    setUploadedFiles(prev => prev.filter(file => file.id !== fileId));
  };

  const handleRetryUpload = async (fileId: string) => {
    setUploadedFiles(prev => 
      prev.map(file => 
        file.id === fileId 
          ? { ...file, status: 'uploading', progress: 0, error: undefined }
          : file
      )
    );

    try {
      await simulateFileUpload(fileId, (progress) => {
        setUploadedFiles(prev => 
          prev.map(file => 
            file.id === fileId 
              ? { ...file, progress }
              : file
          )
        );
      });

      setUploadedFiles(prev => 
        prev.map(file => 
          file.id === fileId 
            ? { ...file, status: 'completed', progress: 100 }
            : file
        )
      );
    } catch (error) {
      setUploadedFiles(prev => 
        prev.map(file => 
          file.id === fileId 
            ? { ...file, status: 'error', error: '업로드 실패' }
            : file
        )
      );
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">파일 업로드</h1>
        <p className="mt-1 text-sm text-gray-600">
          PDF, 이미지, 비디오 등 다양한 형태의 학습 자료를 업로드하세요.
        </p>
      </div>

      {/* Upload Zone */}
      <FileUploadZone 
        onFilesSelected={handleFilesSelected}
        isUploading={isUploading}
      />

      {/* Upload Progress */}
      {isUploading && (
        <UploadProgress 
          files={uploadedFiles.filter(file => file.status === 'uploading')}
        />
      )}

      {/* Uploaded Files List */}
      {uploadedFiles.length > 0 && (
        <UploadedFilesList
          files={uploadedFiles}
          onRemoveFile={handleRemoveFile}
          onRetryUpload={handleRetryUpload}
        />
      )}

      {/* Real-time Status Monitoring */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Temp Storage Monitor */}
        <TempStorageMonitor />

        {/* Active Tasks Monitor */}
        {uploadedFiles.some(file => file.taskId) && (
          <div className="bg-white border rounded-lg p-4">
            <h3 className="text-sm font-medium text-gray-900 mb-3">활성 작업</h3>
            <div className="space-y-2">
              {uploadedFiles
                .filter(file => file.taskId && file.status === 'processing')
                .map(file => (
                  <div key={file.id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                    <span className="text-sm text-gray-700">{file.name}</span>
                    <StatusMonitor
                      taskId={file.taskId!}
                      onComplete={(result) => {
                        setUploadedFiles(prev =>
                          prev.map(f =>
                            f.id === file.id
                              ? { ...f, status: 'completed', documentId: result.document_id }
                              : f
                          )
                        );
                      }}
                      onError={(error) => {
                        setUploadedFiles(prev =>
                          prev.map(f =>
                            f.id === file.id
                              ? { ...f, status: 'error', error }
                              : f
                          )
                        );
                      }}
                    />
                  </div>
                ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
