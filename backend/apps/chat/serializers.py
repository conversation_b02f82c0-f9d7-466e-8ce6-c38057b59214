from rest_framework import serializers
from .models import ChatSession, ChatMessage
from apps.documents.serializers import DocumentListSerializer


class ChatMessageSerializer(serializers.ModelSerializer):
    """채팅 메시지 시리얼라이저"""

    class Meta:
        model = ChatMessage
        fields = [
            'id', 'sender', 'content', 'context', 'created_at',
            'response_time', 'model_used', 'tokens_used'
        ]
        read_only_fields = ['id', 'created_at', 'response_time', 'model_used', 'tokens_used']


class ChatSessionSerializer(serializers.ModelSerializer):
    """채팅 세션 시리얼라이저"""
    messages = ChatMessageSerializer(many=True, read_only=True)
    document = DocumentListSerializer(read_only=True)
    message_count = serializers.SerializerMethodField()
    last_message_at = serializers.SerializerMethodField()

    class Meta:
        model = ChatSession
        fields = [
            'id', 'title', 'document', 'created_at', 'updated_at',
            'is_active', 'messages', 'message_count', 'last_message_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'user']

    def get_message_count(self, obj):
        return obj.messages.count()

    def get_last_message_at(self, obj):
        last_message = obj.messages.last()
        return last_message.created_at if last_message else obj.created_at


class ChatSessionListSerializer(serializers.ModelSerializer):
    """채팅 세션 목록용 간단한 시리얼라이저"""
    document_title = serializers.CharField(source='document.title', read_only=True)
    message_count = serializers.SerializerMethodField()
    last_message_at = serializers.SerializerMethodField()
    last_message_preview = serializers.SerializerMethodField()

    class Meta:
        model = ChatSession
        fields = [
            'id', 'title', 'document_title', 'created_at', 'updated_at',
            'message_count', 'last_message_at', 'last_message_preview'
        ]

    def get_message_count(self, obj):
        return obj.messages.count()

    def get_last_message_at(self, obj):
        last_message = obj.messages.last()
        return last_message.created_at if last_message else obj.created_at

    def get_last_message_preview(self, obj):
        last_message = obj.messages.last()
        if last_message:
            content = last_message.content
            return content[:100] + '...' if len(content) > 100 else content
        return None


class SendMessageSerializer(serializers.Serializer):
    """메시지 전송용 시리얼라이저"""
    message = serializers.CharField(max_length=2000)
    session_id = serializers.IntegerField(required=False, allow_null=True)
    context = serializers.JSONField(required=False, allow_null=True)

    def validate_message(self, value):
        if not value.strip():
            raise serializers.ValidationError("메시지 내용이 비어있습니다.")
        return value.strip()
