"""
문서 처리 관련 Celery 태스크
- 파일 업로드 후 자동 처리
- 청크 생성 및 임베딩 생성
- FAISS 인덱스 업데이트
"""
import logging
from celery import shared_task
from django.core.files.base import ContentFile
from .models import Document, DocumentChunk
from .temp_storage import get_temp_storage
from .file_storage import get_file_storage_manager
from .chunk_service import get_chunk_service
from .embedding_service import embedding_service
from apps.search.faiss_service import faiss_service

logger = logging.getLogger(__name__)


@shared_task(bind=True, max_retries=3)
def process_temp_file_async(self, file_id: str):
    """
    임시 파일을 비동기로 처리
    
    Args:
        file_id: 임시 저장소의 파일 ID
    """
    try:
        temp_storage = get_temp_storage()
        temp_file = temp_storage.get_file(file_id)
        
        if not temp_file:
            logger.error(f"임시 파일을 찾을 수 없습니다: {file_id}")
            return {'success': False, 'error': '파일을 찾을 수 없습니다.'}
        
        # 상태를 처리중으로 변경
        temp_storage.update_status(file_id, 'processing')
        logger.info(f"파일 처리 시작: {temp_file.filename} (ID: {file_id})")
        
        # 1단계: 파일 저장
        file_storage = get_file_storage_manager()
        file_info = file_storage.store_file(
            filename=temp_file.filename,
            content=temp_file.content,
            content_type=temp_file.content_type,
            check_duplicate=True
        )
        
        # 2단계: 문서 생성
        document_data = {
            'title': temp_file.metadata.get('title', temp_file.filename),
            'description': temp_file.metadata.get('description', ''),
            'document_type': temp_file.metadata.get('document_type', 'other'),
            'file_size': file_info['size'],
            'file_type': file_info['content_type'],
            'file_hash': file_info['hash'],
            'processing_status': 'processing',
            'metadata': {
                **temp_file.metadata,
                'file_info': file_info['metadata']
            }
        }
        
        document = Document.objects.create(**document_data)
        document.file.name = file_info['path']
        
        # 썸네일이 있는 경우 설정
        if file_info.get('thumbnail_path'):
            document.thumbnail.name = file_info['thumbnail_path']
        
        document.save()
        
        # 3단계: 청크 생성 (비동기)
        create_chunks_async.delay(document.id)
        
        # 임시 파일 상태 업데이트
        temp_storage.update_status(file_id, 'completed')
        
        logger.info(f"파일 처리 완료: {temp_file.filename} -> Document ID: {document.id}")
        
        return {
            'success': True,
            'document_id': document.id,
            'file_id': file_id,
            'message': '파일 처리가 완료되었습니다.'
        }
        
    except Exception as e:
        # 재시도 로직
        if self.request.retries < self.max_retries:
            logger.warning(f"파일 처리 실패, 재시도 {self.request.retries + 1}/{self.max_retries}: {str(e)}")
            raise self.retry(countdown=60 * (self.request.retries + 1))
        
        # 최종 실패
        temp_storage = get_temp_storage()
        temp_storage.update_status(file_id, 'failed', str(e))
        logger.error(f"파일 처리 최종 실패: {file_id} - {str(e)}")
        
        return {
            'success': False,
            'error': str(e),
            'file_id': file_id
        }


@shared_task(bind=True, max_retries=2)
def create_chunks_async(self, document_id: int):
    """
    문서의 청크를 비동기로 생성
    
    Args:
        document_id: 문서 ID
    """
    try:
        document = Document.objects.get(id=document_id)
        logger.info(f"청크 생성 시작: Document ID {document_id}")
        
        # 청크 생성
        chunk_service = get_chunk_service()
        chunks = chunk_service.create_chunks(document, force_recreate=True)
        
        if chunks:
            # 임베딩 생성 (비동기)
            generate_embeddings_async.delay(document_id)
            
            # 문서 상태 업데이트
            document.processing_status = 'processing'
            document.save(update_fields=['processing_status'])
            
            logger.info(f"청크 생성 완료: Document ID {document_id}, {len(chunks)}개 청크")
            
            return {
                'success': True,
                'document_id': document_id,
                'chunk_count': len(chunks),
                'message': f'청크 생성 완료: {len(chunks)}개'
            }
        else:
            logger.warning(f"청크 생성 실패: Document ID {document_id} - 내용이 없음")
            document.processing_status = 'failed'
            document.save(update_fields=['processing_status'])
            
            return {
                'success': False,
                'document_id': document_id,
                'error': '문서에서 청크를 생성할 수 없습니다.'
            }
        
    except Document.DoesNotExist:
        logger.error(f"문서를 찾을 수 없습니다: Document ID {document_id}")
        return {
            'success': False,
            'error': '문서를 찾을 수 없습니다.',
            'document_id': document_id
        }
    except Exception as e:
        # 재시도 로직
        if self.request.retries < self.max_retries:
            logger.warning(f"청크 생성 실패, 재시도 {self.request.retries + 1}/{self.max_retries}: {str(e)}")
            raise self.retry(countdown=30 * (self.request.retries + 1))
        
        # 최종 실패
        try:
            document = Document.objects.get(id=document_id)
            document.processing_status = 'failed'
            document.save(update_fields=['processing_status'])
        except:
            pass
        
        logger.error(f"청크 생성 최종 실패: Document ID {document_id} - {str(e)}")
        return {
            'success': False,
            'error': str(e),
            'document_id': document_id
        }


@shared_task(bind=True, max_retries=2)
def generate_embeddings_async(self, document_id: int, model: str = 'models/embedding-001'):
    """
    문서 청크들의 임베딩을 비동기로 생성
    
    Args:
        document_id: 문서 ID
        model: 임베딩 모델
    """
    try:
        document = Document.objects.get(id=document_id)
        chunks = list(document.chunks.all())
        
        if not chunks:
            logger.warning(f"청크가 없습니다: Document ID {document_id}")
            return {
                'success': False,
                'error': '청크가 없습니다.',
                'document_id': document_id
            }
        
        logger.info(f"임베딩 생성 시작: Document ID {document_id}, {len(chunks)}개 청크")
        
        # 임베딩 생성
        chunk_service = get_chunk_service()
        success_count = chunk_service.generate_chunk_embeddings(chunks, model=model)
        
        if success_count > 0:
            # FAISS 인덱스 업데이트 (비동기)
            update_faiss_index_async.delay()
            
            # 문서 상태 업데이트
            document.processing_status = 'completed'
            document.save(update_fields=['processing_status'])
            
            logger.info(f"임베딩 생성 완료: Document ID {document_id}, {success_count}/{len(chunks)}")
            
            return {
                'success': True,
                'document_id': document_id,
                'success_count': success_count,
                'total_chunks': len(chunks),
                'model': model,
                'message': f'임베딩 생성 완료: {success_count}/{len(chunks)}'
            }
        else:
            logger.error(f"임베딩 생성 실패: Document ID {document_id}")
            document.processing_status = 'failed'
            document.save(update_fields=['processing_status'])
            
            return {
                'success': False,
                'error': '임베딩 생성에 실패했습니다.',
                'document_id': document_id
            }
        
    except Document.DoesNotExist:
        logger.error(f"문서를 찾을 수 없습니다: Document ID {document_id}")
        return {
            'success': False,
            'error': '문서를 찾을 수 없습니다.',
            'document_id': document_id
        }
    except Exception as e:
        # 재시도 로직
        if self.request.retries < self.max_retries:
            logger.warning(f"임베딩 생성 실패, 재시도 {self.request.retries + 1}/{self.max_retries}: {str(e)}")
            raise self.retry(countdown=60 * (self.request.retries + 1))
        
        # 최종 실패
        try:
            document = Document.objects.get(id=document_id)
            document.processing_status = 'failed'
            document.save(update_fields=['processing_status'])
        except:
            pass
        
        logger.error(f"임베딩 생성 최종 실패: Document ID {document_id} - {str(e)}")
        return {
            'success': False,
            'error': str(e),
            'document_id': document_id
        }


@shared_task
def update_faiss_index_async():
    """FAISS 인덱스를 비동기로 업데이트"""
    try:
        logger.info("FAISS 인덱스 업데이트 시작")
        
        # 인덱스 재구축
        faiss_service.build_indexes(force_rebuild=True)
        
        logger.info("FAISS 인덱스 업데이트 완료")
        
        return {
            'success': True,
            'message': 'FAISS 인덱스 업데이트 완료'
        }
        
    except Exception as e:
        logger.error(f"FAISS 인덱스 업데이트 실패: {str(e)}")
        return {
            'success': False,
            'error': str(e)
        }


@shared_task
def cleanup_temp_storage():
    """임시 저장소 정리 작업"""
    try:
        temp_storage = get_temp_storage()
        temp_storage._cleanup_old_files(max_age_hours=24)
        
        storage_info = temp_storage.get_storage_info()
        logger.info(f"임시 저장소 정리 완료: {storage_info}")
        
        return {
            'success': True,
            'storage_info': storage_info,
            'message': '임시 저장소 정리 완료'
        }
        
    except Exception as e:
        logger.error(f"임시 저장소 정리 실패: {str(e)}")
        return {
            'success': False,
            'error': str(e)
        }


@shared_task(bind=True, max_retries=3)
def reembed_document(self, document_id: int, config_id: int = None):
    """
    특정 문서를 재임베딩

    Args:
        document_id: 문서 ID
        config_id: 임베딩 설정 ID (선택사항)
    """
    try:
        from apps.rag.models import EmbeddingConfig

        # 문서 조회
        document = Document.objects.get(id=document_id)
        logger.info(f"문서 재임베딩 시작: {document.title}")

        # 설정 조회
        if config_id:
            config = EmbeddingConfig.objects.get(id=config_id)
        else:
            config = EmbeddingConfig.objects.filter(is_active=True).first()

        if not config:
            raise ValueError("활성 임베딩 설정을 찾을 수 없습니다.")

        # 기존 청크 삭제
        document.chunks.all().delete()

        # 새로운 청크 생성
        chunk_service = get_chunk_service()
        chunks = chunk_service.create_chunks(
            text=document.content,
            chunk_size=config.chunk_size,
            chunk_overlap=config.chunk_overlap
        )

        # 청크 저장 및 임베딩
        for i, chunk_text in enumerate(chunks):
            # 임베딩 생성
            embedding_vector = embedding_service.generate_embedding(
                text=chunk_text,
                model=config.embedding_model,
                source_type='document',
                source_id=str(document.id)
            )

            # 청크 저장
            DocumentChunk.objects.create(
                document=document,
                content=chunk_text,
                chunk_index=i,
                embedding=embedding_vector
            )

        # FAISS 인덱스 업데이트
        faiss_service.build_indexes(force_rebuild=True)

        logger.info(f"문서 재임베딩 완료: {document.title} ({len(chunks)}개 청크)")
        return {
            'success': True,
            'document_id': document_id,
            'chunks_count': len(chunks),
            'config_used': config.name
        }

    except Exception as e:
        logger.error(f"문서 재임베딩 실패 (ID: {document_id}): {str(e)}")
        return {'success': False, 'error': str(e)}


@shared_task(bind=True, max_retries=3)
def reembed_all_documents(self, user_id: int, config_id: int = None):
    """
    사용자의 모든 문서를 재임베딩

    Args:
        user_id: 사용자 ID
        config_id: 임베딩 설정 ID (선택사항)
    """
    try:
        from django.contrib.auth.models import User
        from apps.rag.models import EmbeddingConfig

        # 사용자 조회
        user = User.objects.get(id=user_id)
        documents = Document.objects.filter(created_by=user)

        logger.info(f"전체 재임베딩 시작: 사용자 {user.username}, {documents.count()}개 문서")

        # 설정 조회
        if config_id:
            config = EmbeddingConfig.objects.get(id=config_id)
        else:
            config = EmbeddingConfig.objects.filter(is_active=True).first()

        if not config:
            raise ValueError("활성 임베딩 설정을 찾을 수 없습니다.")

        success_count = 0
        error_count = 0

        for document in documents:
            try:
                # 각 문서를 개별적으로 재임베딩
                result = reembed_document.apply(args=[document.id, config.id])
                if result.get('success'):
                    success_count += 1
                else:
                    error_count += 1
                    logger.error(f"문서 재임베딩 실패: {document.title}")

            except Exception as e:
                error_count += 1
                logger.error(f"문서 재임베딩 오류: {document.title} - {str(e)}")

        logger.info(f"전체 재임베딩 완료: 성공 {success_count}개, 실패 {error_count}개")
        return {
            'success': True,
            'total_documents': documents.count(),
            'success_count': success_count,
            'error_count': error_count,
            'config_used': config.name
        }

    except Exception as e:
        logger.error(f"전체 재임베딩 실패 (사용자 ID: {user_id}): {str(e)}")
        return {'success': False, 'error': str(e)}
