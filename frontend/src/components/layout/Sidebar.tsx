'use client';

import { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  Home,
  FileText,
  Upload,
  Search,
  Settings,
  X,
  ChevronDown,
  ChevronRight,
  Folder,
  Image,
  Video,
  FileImage,
  MessageSquare,
  Database
} from 'lucide-react';
import { clsx } from 'clsx';

interface SidebarProps {
  isOpen: boolean;
  onClose: () => void;
}

interface NavItem {
  name: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  children?: NavItem[];
}

const navigation: NavItem[] = [
  { name: '대시보드', href: '/', icon: Home },
  { name: '문서 관리', href: '/documents', icon: FileText },
  { name: '업로드', href: '/upload', icon: Upload },
  { name: 'AI 채팅', href: '/chat', icon: MessageSquare },
  { name: '검색', href: '/search', icon: Search },
  {
    name: '카테고리',
    href: '/categories',
    icon: Folder,
    children: [
      { name: '강의 자료', href: '/categories/lectures', icon: FileText },
      { name: '이미지', href: '/categories/images', icon: Image },
      { name: '비디오', href: '/categories/videos', icon: Video },
      { name: '스크린샷', href: '/categories/screenshots', icon: FileImage },
    ]
  },
  { name: 'RAG 관리', href: '/rag', icon: Database },
  { name: '설정', href: '/settings', icon: Settings },
];

export function Sidebar({ isOpen, onClose }: SidebarProps) {
  const pathname = usePathname();
  const [expandedItems, setExpandedItems] = useState<string[]>([]);

  const toggleExpanded = (href: string) => {
    setExpandedItems(prev => 
      prev.includes(href) 
        ? prev.filter(item => item !== href)
        : [...prev, href]
    );
  };

  const renderNavItem = (item: NavItem, level = 0) => {
    const isActive = pathname === item.href;
    const isExpanded = expandedItems.includes(item.href);
    const hasChildren = item.children && item.children.length > 0;

    return (
      <div key={item.href}>
        <div className="flex items-center">
          <Link
            href={item.href}
            className={clsx(
              'flex-1 flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors',
              level > 0 && 'ml-6',
              isActive
                ? 'bg-blue-100 text-blue-700'
                : 'text-gray-700 hover:text-gray-900 hover:bg-gray-100'
            )}
            onClick={() => {
              if (window.innerWidth < 1024) {
                onClose();
              }
            }}
          >
            <item.icon className="mr-3 h-5 w-5 flex-shrink-0" />
            {item.name}
          </Link>
          
          {hasChildren && (
            <button
              onClick={() => toggleExpanded(item.href)}
              className="p-1 rounded-md text-gray-400 hover:text-gray-600"
            >
              {isExpanded ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronRight className="h-4 w-4" />
              )}
            </button>
          )}
        </div>
        
        {hasChildren && isExpanded && (
          <div className="mt-1 space-y-1">
            {item.children!.map(child => renderNavItem(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <>
      {/* Mobile overlay */}
      {isOpen && (
        <div 
          className="fixed inset-0 z-40 lg:hidden"
          onClick={onClose}
        >
          <div className="fixed inset-0 bg-gray-600 bg-opacity-75" />
        </div>
      )}

      {/* Sidebar */}
      <div className={clsx(
        'fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0',
        isOpen ? 'translate-x-0' : '-translate-x-full'
      )}>
        <div className="flex flex-col h-full">
          {/* Mobile close button */}
          <div className="flex items-center justify-between p-4 lg:hidden">
            <span className="text-lg font-semibold text-gray-900">메뉴</span>
            <button
              onClick={onClose}
              className="p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100"
            >
              <X className="h-6 w-6" />
            </button>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-4 py-4 space-y-2 overflow-y-auto">
            {navigation.map(item => renderNavItem(item))}
          </nav>

          {/* Footer */}
          <div className="p-4 border-t border-gray-200">
            <div className="text-xs text-gray-500 text-center">
              지식 관리 시스템 v1.0
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
