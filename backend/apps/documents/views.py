from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q, Count
from django.core.files.base import ContentFile
from .models import Document, DocumentChunk
from .serializers import DocumentSerializer, DocumentListSerializer, DocumentChunkSerializer
from .temp_storage import get_temp_storage
from .file_storage import get_file_storage_manager
from .chunk_service import get_chunk_service
from .tasks import process_temp_file_async, create_chunks_async, generate_embeddings_async
import logging

logger = logging.getLogger(__name__)


class DocumentViewSet(viewsets.ModelViewSet):
    """문서 ViewSet"""
    queryset = Document.objects.all()
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['document_type', 'category', 'tags', 'processing_status', 'is_favorite']
    search_fields = ['title', 'description', 'content', 'extracted_text']
    ordering_fields = ['created_at', 'updated_at', 'view_count', 'title']
    ordering = ['-created_at']

    def get_serializer_class(self):
        if self.action == 'list':
            return DocumentListSerializer
        return DocumentSerializer

    def retrieve(self, request, *args, **kwargs):
        """문서 상세 조회 시 조회수 증가"""
        instance = self.get_object()
        instance.view_count += 1
        instance.save(update_fields=['view_count'])
        serializer = self.get_serializer(instance)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def toggle_favorite(self, request, pk=None):
        """즐겨찾기 토글"""
        document = self.get_object()
        document.is_favorite = not document.is_favorite
        document.save(update_fields=['is_favorite'])
        return Response({
            'status': 'success',
            'is_favorite': document.is_favorite
        })

    @action(detail=False, methods=['get'])
    def favorites(self, request):
        """즐겨찾기 문서 목록"""
        queryset = self.get_queryset().filter(is_favorite=True)
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def recent(self, request):
        """최근 문서 목록"""
        queryset = self.get_queryset().order_by('-created_at')[:10]
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def stats(self, request):
        """문서 통계"""
        queryset = self.get_queryset()
        stats = {
            'total_documents': queryset.count(),
            'by_type': {},
            'by_status': {},
            'favorites_count': queryset.filter(is_favorite=True).count(),
        }
        
        # 타입별 통계
        for doc_type, _ in Document.DOCUMENT_TYPES:
            count = queryset.filter(document_type=doc_type).count()
            stats['by_type'][doc_type] = count
        
        # 상태별 통계
        for status_type, _ in Document.PROCESSING_STATUS:
            count = queryset.filter(processing_status=status_type).count()
            stats['by_status'][status_type] = count
        
        return Response(stats)

    @action(detail=True, methods=['get'])
    def chunks(self, request, pk=None):
        """문서의 청크 목록"""
        document = self.get_object()
        chunks = document.chunks.all().order_by('chunk_index')
        serializer = DocumentChunkSerializer(chunks, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def search_content(self, request):
        """문서 내용 검색"""
        query = request.GET.get('q', '')
        if not query:
            return Response({'error': '검색어가 필요합니다.'}, status=status.HTTP_400_BAD_REQUEST)
        
        queryset = self.get_queryset().filter(
            Q(title__icontains=query) |
            Q(description__icontains=query) |
            Q(content__icontains=query) |
            Q(extracted_text__icontains=query)
        )
        
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def toggle_favorite(self, request, pk=None):
        """즐겨찾기 토글"""
        document = self.get_object()
        document.is_favorite = not document.is_favorite
        document.save()

        return Response({
            'is_favorite': document.is_favorite,
            'message': '즐겨찾기에 추가되었습니다.' if document.is_favorite else '즐겨찾기에서 제거되었습니다.'
        })

    @action(detail=False, methods=['post'])
    def temp_upload(self, request):
        """임시 파일 업로드"""
        try:
            file = request.FILES.get('file')
            if not file:
                return Response(
                    {'error': '파일이 필요합니다.'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # 파일 크기 체크 (100MB 제한)
            max_size = 100 * 1024 * 1024  # 100MB
            if file.size > max_size:
                return Response(
                    {'error': f'파일 크기가 너무 큽니다. 최대 {max_size // (1024*1024)}MB까지 가능합니다.'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # 메타데이터 수집
            metadata = {
                'original_filename': file.name,
                'user_id': request.user.id,
                'title': request.data.get('title', file.name),
                'description': request.data.get('description', ''),
                'document_type': request.data.get('document_type', 'other'),
            }

            # 임시 저장소에 저장
            temp_storage = get_temp_storage()
            file_id = temp_storage.store_file(
                filename=file.name,
                content=file.read(),
                content_type=file.content_type,
                metadata=metadata
            )

            logger.info(f"임시 파일 업로드 완료: {file.name} (ID: {file_id})")

            return Response({
                'file_id': file_id,
                'filename': file.name,
                'size': file.size,
                'content_type': file.content_type,
                'status': 'uploaded',
                'message': '파일이 임시 저장되었습니다. 처리를 시작합니다.'
            })

        except ValueError as e:
            logger.error(f"임시 파일 업로드 오류: {str(e)}")
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f"임시 파일 업로드 예외: {str(e)}")
            return Response(
                {'error': '파일 업로드 중 오류가 발생했습니다.'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'])
    def temp_status(self, request):
        """임시 파일 상태 조회"""
        file_id = request.GET.get('file_id')
        if not file_id:
            return Response(
                {'error': 'file_id가 필요합니다.'},
                status=status.HTTP_400_BAD_REQUEST
            )

        temp_storage = get_temp_storage()
        temp_file = temp_storage.get_file(file_id)

        if not temp_file:
            return Response(
                {'error': '파일을 찾을 수 없습니다.'},
                status=status.HTTP_404_NOT_FOUND
            )

        return Response(temp_file.to_dict())

    @action(detail=False, methods=['get'])
    def temp_list(self, request):
        """임시 파일 목록 조회"""
        temp_storage = get_temp_storage()
        status_filter = request.GET.get('status')
        files = temp_storage.list_files(status=status_filter)

        return Response({
            'files': files,
            'storage_info': temp_storage.get_storage_info()
        })

    @action(detail=False, methods=['post'])
    def process_temp_file(self, request):
        """임시 파일을 정식 문서로 처리"""
        file_id = request.data.get('file_id')
        if not file_id:
            return Response(
                {'error': 'file_id가 필요합니다.'},
                status=status.HTTP_400_BAD_REQUEST
            )

        temp_storage = get_temp_storage()
        temp_file = temp_storage.get_file(file_id)

        if not temp_file:
            return Response(
                {'error': '파일을 찾을 수 없습니다.'},
                status=status.HTTP_404_NOT_FOUND
            )

        try:
            # 상태를 처리중으로 변경
            temp_storage.update_status(file_id, 'processing')

            # 개선된 파일 저장 시스템 사용
            file_storage = get_file_storage_manager()
            file_info = file_storage.store_file(
                filename=temp_file.filename,
                content=temp_file.content,
                content_type=temp_file.content_type,
                check_duplicate=True
            )

            # 문서 생성
            document_data = {
                'title': temp_file.metadata.get('title', temp_file.filename),
                'description': temp_file.metadata.get('description', ''),
                'document_type': temp_file.metadata.get('document_type', 'other'),
                'file_size': file_info['size'],
                'file_type': file_info['content_type'],
                'file_hash': file_info['hash'],
                'processing_status': 'pending',
                'metadata': {
                    **temp_file.metadata,
                    'file_info': file_info['metadata']
                }
            }

            # 파일 경로 설정
            file_content = ContentFile(temp_file.content, name=file_info['filename'])

            document = Document.objects.create(**document_data)
            document.file.name = file_info['path']

            # 썸네일이 있는 경우 설정
            if file_info.get('thumbnail_path'):
                document.thumbnail.name = file_info['thumbnail_path']

            document.save()

            # 임시 파일 상태 업데이트
            temp_storage.update_status(file_id, 'completed')

            logger.info(f"임시 파일 처리 완료: {temp_file.filename} -> Document ID: {document.id}")

            serializer = DocumentSerializer(document)
            return Response({
                'document': serializer.data,
                'message': '파일이 성공적으로 처리되었습니다.'
            })

        except Exception as e:
            # 오류 발생시 상태 업데이트
            temp_storage.update_status(file_id, 'failed', str(e))
            logger.error(f"임시 파일 처리 실패: {temp_file.filename} - {str(e)}")

            return Response(
                {'error': '파일 처리 중 오류가 발생했습니다.'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'])
    def storage_stats(self, request):
        """파일 저장소 통계"""
        try:
            file_storage = get_file_storage_manager()
            storage_stats = file_storage.get_storage_stats()

            temp_storage = get_temp_storage()
            temp_stats = temp_storage.get_storage_info()

            return Response({
                'permanent_storage': storage_stats,
                'temporary_storage': temp_stats,
                'message': '저장소 통계 조회 완료'
            })

        except Exception as e:
            logger.error(f"저장소 통계 조회 실패: {str(e)}")
            return Response(
                {'error': '저장소 통계 조회 중 오류가 발생했습니다.'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['post'])
    def create_chunks(self, request, pk=None):
        """문서의 청크 생성"""
        document = self.get_object()
        force_recreate = request.data.get('force_recreate', False)

        try:
            chunk_service = get_chunk_service()
            chunks = chunk_service.create_chunks(document, force_recreate=force_recreate)

            # 청크 시리얼라이저로 응답
            serializer = DocumentChunkSerializer(chunks, many=True)

            return Response({
                'chunks': serializer.data,
                'count': len(chunks),
                'message': f'청크 생성 완료: {len(chunks)}개'
            })

        except Exception as e:
            logger.error(f"청크 생성 실패: {str(e)}")
            return Response(
                {'error': '청크 생성 중 오류가 발생했습니다.'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['post'])
    def generate_embeddings(self, request, pk=None):
        """문서 청크들의 임베딩 생성"""
        document = self.get_object()
        model = request.data.get('model', 'models/embedding-001')

        try:
            # 청크가 없으면 먼저 생성
            if not document.chunks.exists():
                chunk_service = get_chunk_service()
                chunk_service.create_chunks(document)

            chunks = list(document.chunks.all())
            chunk_service = get_chunk_service()
            success_count = chunk_service.generate_chunk_embeddings(chunks, model=model)

            return Response({
                'success_count': success_count,
                'total_chunks': len(chunks),
                'model': model,
                'message': f'임베딩 생성 완료: {success_count}/{len(chunks)}'
            })

        except Exception as e:
            logger.error(f"임베딩 생성 실패: {str(e)}")
            return Response(
                {'error': '임베딩 생성 중 오류가 발생했습니다.'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'])
    def chunk_stats(self, request):
        """청크 통계"""
        try:
            chunk_service = get_chunk_service()
            stats = chunk_service.get_chunk_stats()

            return Response({
                'stats': stats,
                'message': '청크 통계 조회 완료'
            })

        except Exception as e:
            logger.error(f"청크 통계 조회 실패: {str(e)}")
            return Response(
                {'error': '청크 통계 조회 중 오류가 발생했습니다.'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['post'])
    def process_temp_file_async(self, request):
        """임시 파일을 비동기로 처리"""
        file_id = request.data.get('file_id')
        if not file_id:
            return Response(
                {'error': 'file_id가 필요합니다.'},
                status=status.HTTP_400_BAD_REQUEST
            )

        temp_storage = get_temp_storage()
        temp_file = temp_storage.get_file(file_id)

        if not temp_file:
            return Response(
                {'error': '파일을 찾을 수 없습니다.'},
                status=status.HTTP_404_NOT_FOUND
            )

        try:
            # 비동기 태스크 실행
            task = process_temp_file_async.delay(file_id)

            logger.info(f"비동기 파일 처리 시작: {temp_file.filename} (Task ID: {task.id})")

            return Response({
                'task_id': task.id,
                'file_id': file_id,
                'filename': temp_file.filename,
                'status': 'processing',
                'message': '파일 처리가 시작되었습니다. 완료까지 시간이 소요될 수 있습니다.'
            })

        except Exception as e:
            logger.error(f"비동기 파일 처리 시작 실패: {str(e)}")
            return Response(
                {'error': '파일 처리 시작 중 오류가 발생했습니다.'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['post'])
    def create_chunks_async(self, request, pk=None):
        """문서의 청크를 비동기로 생성"""
        document = self.get_object()

        try:
            # 비동기 태스크 실행
            task = create_chunks_async.delay(document.id)

            logger.info(f"비동기 청크 생성 시작: Document ID {document.id} (Task ID: {task.id})")

            return Response({
                'task_id': task.id,
                'document_id': document.id,
                'status': 'processing',
                'message': '청크 생성이 시작되었습니다.'
            })

        except Exception as e:
            logger.error(f"비동기 청크 생성 시작 실패: {str(e)}")
            return Response(
                {'error': '청크 생성 시작 중 오류가 발생했습니다.'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['post'])
    def generate_embeddings_async(self, request, pk=None):
        """문서 청크들의 임베딩을 비동기로 생성"""
        document = self.get_object()
        model = request.data.get('model', 'models/embedding-001')

        try:
            # 비동기 태스크 실행
            task = generate_embeddings_async.delay(document.id, model)

            logger.info(f"비동기 임베딩 생성 시작: Document ID {document.id} (Task ID: {task.id})")

            return Response({
                'task_id': task.id,
                'document_id': document.id,
                'model': model,
                'status': 'processing',
                'message': '임베딩 생성이 시작되었습니다.'
            })

        except Exception as e:
            logger.error(f"비동기 임베딩 생성 시작 실패: {str(e)}")
            return Response(
                {'error': '임베딩 생성 시작 중 오류가 발생했습니다.'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'])
    def task_status(self, request):
        """태스크 상태 조회"""
        from celery.result import AsyncResult

        task_id = request.GET.get('task_id')
        if not task_id:
            return Response(
                {'error': 'task_id가 필요합니다.'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            result = AsyncResult(task_id)

            response_data = {
                'task_id': task_id,
                'status': result.status,
                'ready': result.ready(),
                'successful': result.successful() if result.ready() else None,
                'failed': result.failed() if result.ready() else None,
            }

            if result.ready():
                if result.successful():
                    response_data['result'] = result.result
                elif result.failed():
                    response_data['error'] = str(result.result)
            else:
                response_data['info'] = result.info

            return Response(response_data)

        except Exception as e:
            logger.error(f"태스크 상태 조회 실패: {str(e)}")
            return Response(
                {'error': '태스크 상태 조회 중 오류가 발생했습니다.'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
