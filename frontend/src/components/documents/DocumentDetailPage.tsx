'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  ArrowLeft,
  Download,
  Edit,
  Trash2,
  Heart,
  Share2,
  Eye,
  FileText,
  Image,
  Video,
  File,
  Calendar,
  User,
  Tag,
  MessageSquare,
  Send
} from 'lucide-react';
import { documentsApi, chatApi } from '@/lib/api';

interface DocumentDetailPageProps {
  documentId: string;
}

interface Document {
  id: string;
  title: string;
  description: string;
  document_type: string;
  content: string;
  file: string;
  file_size: number;
  created_at: string;
  updated_at: string;
  category: {
    id: string;
    name: string;
  } | null;
  tags: Array<{
    id: string;
    name: string;
  }>;
  processing_status: string;
  is_favorite: boolean;
  view_count: number;
  metadata: any;
}

interface ChatMessage {
  id: string;
  content: string;
  sender: 'user' | 'ai';
  timestamp: string;
}

export function DocumentDetailPage({ documentId }: DocumentDetailPageProps) {
  const router = useRouter();
  const [document, setDocument] = useState<Document | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showChat, setShowChat] = useState(false);


  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
  const [chatInput, setChatInput] = useState('');
  const [isSending, setIsSending] = useState(false);
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null);

  useEffect(() => {
    loadDocument();
    // 새로운 문서로 이동할 때 채팅 세션 초기화
    setChatMessages([]);
    setCurrentSessionId(null);
    setShowChat(false);
  }, [documentId]);

  const loadDocument = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await documentsApi.getDocument(documentId);
      setDocument(response);
    } catch (err) {
      setError('문서를 불러오는데 실패했습니다.');
      console.error('Error loading document:', err);
    } finally {
      setLoading(false);
    }
  };

  const sendChatMessage = async () => {
    if (!chatInput.trim() || isSending || !document) return;

    const messageContent = chatInput.trim(); // 메시지 내용을 미리 저장

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      content: messageContent,
      sender: 'user',
      timestamp: new Date().toISOString()
    };

    setChatMessages(prev => [...prev, userMessage]);
    setChatInput(''); // 입력 필드 초기화
    setIsSending(true);

    try {
      // 문서 컨텍스트와 함께 메시지 전송
      const requestData = {
        message: messageContent, // 저장된 메시지 내용 사용
        sessionId: currentSessionId, // 멀티턴을 위한 세션 ID
        context: {
          documentId: document.id.toString(), // 문자열로 변환
          documentTitle: document.title,
          documentContent: document.content?.substring(0, 2000) // 처음 2000자만 컨텍스트로 사용
        }
      };

      console.log('Sending chat request:', requestData);
      const response = await chatApi.sendMessage(requestData);

      // 세션 ID 저장 (멀티턴 대화를 위해)
      if (response.session_id && !currentSessionId) {
        setCurrentSessionId(response.session_id.toString());
      }

      const aiMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        content: response.response || response.ai_message?.content || '죄송합니다. 응답을 생성할 수 없습니다.',
        sender: 'ai',
        timestamp: new Date().toISOString()
      };

      setChatMessages(prev => [...prev, aiMessage]);
    } catch (err) {
      console.error('Chat error:', err);
      console.error('Error details:', {
        message: err.message,
        response: err.response?.data,
        status: err.response?.status
      });

      let errorContent = '죄송합니다. 메시지 전송 중 오류가 발생했습니다.';

      if (err.response?.data?.error) {
        errorContent = err.response.data.error;
      } else if (err.response?.status === 400) {
        errorContent = '잘못된 요청입니다. 다시 시도해주세요.';
      } else if (err.response?.status === 401) {
        errorContent = '인증이 필요합니다. 로그인해주세요.';
      } else if (err.response?.status >= 500) {
        errorContent = '서버 오류가 발생했습니다. 잠시 후 다시 시도해주세요.';
      }

      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        content: errorContent,
        sender: 'ai',
        timestamp: new Date().toISOString()
      };
      setChatMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsSending(false);
    }
  };

  const handleChatKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendChatMessage();
    }
  };

  const handleToggleFavorite = async () => {
    if (!document) return;
    
    try {
      await documentsApi.toggleFavorite(documentId);
      setDocument(prev => prev ? { ...prev, is_favorite: !prev.is_favorite } : null);
    } catch (err) {
      console.error('Error toggling favorite:', err);
    }
  };

  const handleDelete = async () => {
    if (!document || !confirm('정말로 이 문서를 삭제하시겠습니까?')) return;
    
    try {
      await documentsApi.deleteDocument(documentId);
      router.push('/documents');
    } catch (err) {
      console.error('Error deleting document:', err);
      alert('문서 삭제에 실패했습니다.');
    }
  };

  const getFileIcon = (type: string) => {
    switch (type) {
      case 'pdf':
        return FileText;
      case 'image':
        return Image;
      case 'video':
        return Video;
      default:
        return File;
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('ko-KR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-96">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error || !document) {
    return (
      <div className="text-center py-12">
        <p className="text-red-600 mb-4">{error || '문서를 찾을 수 없습니다.'}</p>
        <button 
          onClick={() => router.push('/documents')}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          문서 목록으로 돌아가기
        </button>
      </div>
    );
  }

  const Icon = getFileIcon(document.document_type);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <button
          onClick={() => router.back()}
          className="flex items-center text-gray-600 hover:text-gray-900"
        >
          <ArrowLeft className="h-5 w-5 mr-2" />
          뒤로 가기
        </button>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={handleToggleFavorite}
            className={`p-2 rounded-md ${
              document.is_favorite 
                ? 'text-red-500 hover:text-red-600' 
                : 'text-gray-400 hover:text-red-500'
            }`}
          >
            <Heart className={`h-5 w-5 ${document.is_favorite ? 'fill-current' : ''}`} />
          </button>
          
          <button className="p-2 text-gray-400 hover:text-gray-600 rounded-md">
            <Share2 className="h-5 w-5" />
          </button>
          
          <button className="p-2 text-gray-400 hover:text-gray-600 rounded-md">
            <Edit className="h-5 w-5" />
          </button>
          
          <button 
            onClick={handleDelete}
            className="p-2 text-gray-400 hover:text-red-600 rounded-md"
          >
            <Trash2 className="h-5 w-5" />
          </button>
        </div>
      </div>

      {/* Document Info */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-start space-x-4">
            <div className="flex-shrink-0">
              <Icon className="h-12 w-12 text-blue-500" />
            </div>
            
            <div className="flex-1 min-w-0">
              <h1 className="text-2xl font-bold text-gray-900 mb-2">{document.title}</h1>
              {document.description && (
                <p className="text-gray-600 mb-4">{document.description}</p>
              )}
              
              <div className="flex flex-wrap items-center gap-4 text-sm text-gray-500">
                <div className="flex items-center">
                  <Calendar className="h-4 w-4 mr-1" />
                  {formatDate(document.created_at)}
                </div>
                
                <div className="flex items-center">
                  <File className="h-4 w-4 mr-1" />
                  {formatFileSize(document.file_size || 0)}
                </div>
                
                <div className="flex items-center">
                  <Eye className="h-4 w-4 mr-1" />
                  조회 {document.view_count}회
                </div>
                
                {document.category && (
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    {document.category.name}
                  </span>
                )}
              </div>
              
              {document.tags.length > 0 && (
                <div className="flex flex-wrap gap-2 mt-3">
                  {document.tags.map(tag => (
                    <span key={tag.id} className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-800">
                      <Tag className="h-3 w-3 mr-1" />
                      {tag.name}
                    </span>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
        
        <div className="px-6 py-4">
          <div className="flex items-center space-x-4">
            {document.file && (
              <a
                href={document.file}
                download
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
              >
                <Download className="h-4 w-4 mr-2" />
                다운로드
              </a>
            )}
            
            <button
              onClick={() => setShowChat(!showChat)}
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              <MessageSquare className="h-4 w-4 mr-2" />
              AI 채팅
            </button>
          </div>
        </div>
      </div>

      {/* Document Content */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-medium text-gray-900">문서 내용</h2>
        </div>
        
        <div className="px-6 py-4">
          {document.content ? (
            <div className="prose max-w-none">
              <pre className="whitespace-pre-wrap text-sm text-gray-700 font-sans">
                {document.content}
              </pre>
            </div>
          ) : (
            <p className="text-gray-500 italic">추출된 텍스트 내용이 없습니다.</p>
          )}
        </div>
      </div>

      {/* AI Chat Panel - 중앙 모달 스타일 */}
      {showChat && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4"
          style={{zIndex: 9999}}
          onClick={() => setShowChat(false)}
        >
          <div
            className="bg-white rounded-lg shadow-xl border border-gray-200 w-full max-w-2xl h-[600px] flex flex-col"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="px-6 py-4 border-b border-gray-200 flex-shrink-0">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-lg font-medium text-gray-900">AI 채팅</h2>
                  <p className="text-sm text-gray-600">이 문서에 대해 질문해보세요.</p>
                </div>
                <div className="flex items-center space-x-3">
                  {currentSessionId && (
                    <div className="text-xs text-gray-500">
                      세션 #{currentSessionId}
                    </div>
                  )}
                  <button
                    onClick={() => setShowChat(false)}
                    className="text-gray-400 hover:text-gray-600 transition-colors"
                  >
                    <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>

            <div className="flex-1 flex flex-col px-6 py-4">
              {/* Chat Messages */}
              <div className="flex-1 border border-gray-200 rounded-md p-4 mb-4 overflow-y-auto bg-gray-50 space-y-3">
              {chatMessages.length === 0 ? (
                <div className="text-center text-gray-500">
                  <MessageSquare className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                  <p>문서에 대해 궁금한 것을 질문해보세요!</p>
                  <p className="text-sm mt-1">예: "이 문서의 주요 내용을 요약해주세요"</p>
                </div>
              ) : (
                chatMessages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
                  >
                    <div
                      className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                        message.sender === 'user'
                          ? 'bg-blue-600 text-white'
                          : 'bg-white border border-gray-200 text-gray-900'
                      }`}
                    >
                      <p className="text-sm">{message.content}</p>
                      <p className={`text-xs mt-1 ${
                        message.sender === 'user' ? 'text-blue-100' : 'text-gray-500'
                      }`}>
                        {new Date(message.timestamp).toLocaleTimeString('ko-KR', {
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </p>
                    </div>
                  </div>
                ))
              )}
              {isSending && (
                <div className="flex justify-start">
                  <div className="bg-white border border-gray-200 text-gray-900 max-w-xs lg:max-w-md px-4 py-2 rounded-lg">
                    <div className="flex items-center space-x-2">
                      <div className="flex space-x-1">
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                      </div>
                      <span className="text-sm text-gray-500">AI가 답변을 생성하고 있습니다...</span>
                    </div>
                  </div>
                </div>
              )}
            </div>

              {/* Chat Input */}
              <div className="flex space-x-2 flex-shrink-0">
              <input
                type="text"
                value={chatInput}
                onChange={(e) => setChatInput(e.target.value)}
                onKeyPress={handleChatKeyPress}
                placeholder="문서에 대해 질문해보세요..."
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-gray-900 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                disabled={isSending}
              />
              <button
                onClick={sendChatMessage}
                disabled={!chatInput.trim() || isSending}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
              >
                <Send className="h-4 w-4" />
              </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
