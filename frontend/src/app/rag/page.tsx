'use client';

import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Box,
  Paper,
  Tabs,
  Tab,
  Grid,
  Card,
  CardContent,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
  Settings as SettingsIcon,
  Analytics as AnalyticsIcon,
  Storage as StorageIcon,
  Build as BuildIcon
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import api from '../../lib/api';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`rag-tabpanel-${index}`}
      aria-labelledby={`rag-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

interface Stats {
  embedding_stats: {
    total: number;
    gemini: number;
    openai: number;
  };
  faiss_stats: {
    gemini_index_size: number;
    openai_index_size: number;
  };
}

interface Embedding {
  id: number;
  text_preview: string;
  embedding_model: string;
  source_type: string;
  hit_count: number;
  created_at: string;
}

interface Config {
  id: number;
  name: string;
  chunk_size: number;
  chunk_overlap: number;
  embedding_model: string;
  is_active: boolean;
}

interface SearchResult {
  score: number;
  source: string;
  text: string;
}

export default function RAGManagement() {
  const { user } = useAuth();
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(false);
  const [stats, setStats] = useState<Stats | null>(null);
  const [embeddings, setEmbeddings] = useState<Embedding[]>([]);
  const [configs, setConfigs] = useState<Config[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<{results: SearchResult[]} | null>(null);
  const [configDialog, setConfigDialog] = useState(false);
  const [newConfig, setNewConfig] = useState({
    name: '',
    chunk_size: 1000,
    chunk_overlap: 200,
    embedding_model: 'models/embedding-001'
  });

  useEffect(() => {
    loadStats();
    loadEmbeddings();
    loadConfigs();
  }, []);

  const loadStats = async () => {
    try {
      const response = await api.get('/api/rag/management/stats/');
      setStats(response.data);
    } catch (error) {
      console.error('통계 로드 실패:', error);
    }
  };

  const loadEmbeddings = async () => {
    try {
      const response = await api.get('/api/rag/management/embeddings/');
      setEmbeddings(response.data.results || []);
    } catch (error) {
      console.error('임베딩 목록 로드 실패:', error);
    }
  };

  const loadConfigs = async () => {
    try {
      const response = await api.get('/api/rag/configs/');
      setConfigs(response.data.results || []);
    } catch (error) {
      console.error('설정 목록 로드 실패:', error);
    }
  };

  const handleRebuildIndex = async () => {
    setLoading(true);
    try {
      await api.post('/api/rag/management/rebuild_index/', {
        force_rebuild: true
      });
      alert('FAISS 인덱스가 성공적으로 재구축되었습니다.');
      loadStats();
    } catch (error) {
      console.error('인덱스 재구축 실패:', error);
      alert('인덱스 재구축에 실패했습니다.');
    } finally {
      setLoading(false);
    }
  };

  const handleTestSearch = async () => {
    if (!searchQuery.trim()) return;
    
    setLoading(true);
    try {
      const response = await api.post('/api/rag/management/test_search/', {
        query: searchQuery,
        k: 5,
        strategy: 'hybrid'
      });
      setSearchResults(response.data);
    } catch (error) {
      console.error('검색 테스트 실패:', error);
      alert('검색 테스트에 실패했습니다.');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteEmbeddings = async (embeddingIds: number[]) => {
    if (!confirm('선택한 임베딩을 삭제하시겠습니까?')) return;
    
    setLoading(true);
    try {
      await api.delete('/api/rag/management/delete_embeddings/', {
        data: {
          embedding_ids: embeddingIds,
          rebuild_index: true
        }
      });
      alert('임베딩이 삭제되었습니다.');
      loadEmbeddings();
      loadStats();
    } catch (error) {
      console.error('임베딩 삭제 실패:', error);
      alert('임베딩 삭제에 실패했습니다.');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateConfig = async () => {
    try {
      await api.post('/api/rag/configs/', newConfig);
      setConfigDialog(false);
      setNewConfig({
        name: '',
        chunk_size: 1000,
        chunk_overlap: 200,
        embedding_model: 'models/embedding-001'
      });
      loadConfigs();
      alert('설정이 생성되었습니다.');
    } catch (error) {
      console.error('설정 생성 실패:', error);
      alert('설정 생성에 실패했습니다.');
    }
  };

  const handleActivateConfig = async (configId: number) => {
    try {
      await api.post(`/api/rag/configs/${configId}/activate/`);
      loadConfigs();
      alert('설정이 활성화되었습니다.');
    } catch (error) {
      console.error('설정 활성화 실패:', error);
      alert('설정 활성화에 실패했습니다.');
    }
  };

  return (
    <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
      <Typography variant="h4" gutterBottom>
        RAG 시스템 관리
      </Typography>

      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={tabValue} onChange={(e, newValue) => setTabValue(newValue)}>
          <Tab icon={<AnalyticsIcon />} label="통계" />
          <Tab icon={<StorageIcon />} label="임베딩 관리" />
          <Tab icon={<SearchIcon />} label="검색 테스트" />
          <Tab icon={<SettingsIcon />} label="설정" />
        </Tabs>
      </Box>

      {/* 통계 탭 */}
      <TabPanel value={tabValue} index={0}>
        {stats && (
          <Grid container spacing={3}>
            <Grid item xs={12} md={3}>
              <Card>
                <CardContent>
                  <Typography color="textSecondary" gutterBottom>
                    총 임베딩
                  </Typography>
                  <Typography variant="h4">
                    {stats.embedding_stats?.total || 0}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={3}>
              <Card>
                <CardContent>
                  <Typography color="textSecondary" gutterBottom>
                    Gemini 임베딩
                  </Typography>
                  <Typography variant="h4">
                    {stats.embedding_stats?.gemini || 0}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={3}>
              <Card>
                <CardContent>
                  <Typography color="textSecondary" gutterBottom>
                    OpenAI 임베딩
                  </Typography>
                  <Typography variant="h4">
                    {stats.embedding_stats?.openai || 0}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={3}>
              <Card>
                <CardContent>
                  <Typography color="textSecondary" gutterBottom>
                    FAISS 인덱스
                  </Typography>
                  <Typography variant="h6">
                    Gemini: {stats.faiss_stats?.gemini_index_size || 0}
                  </Typography>
                  <Typography variant="h6">
                    OpenAI: {stats.faiss_stats?.openai_index_size || 0}
                  </Typography>
                  <Button
                    variant="outlined"
                    size="small"
                    startIcon={<BuildIcon />}
                    onClick={handleRebuildIndex}
                    disabled={loading}
                    sx={{ mt: 1 }}
                  >
                    재구축
                  </Button>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        )}
      </TabPanel>

      {/* 임베딩 관리 탭 */}
      <TabPanel value={tabValue} index={1}>
        <Box sx={{ mb: 2 }}>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={loadEmbeddings}
            disabled={loading}
          >
            새로고침
          </Button>
        </Box>
        
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>텍스트 미리보기</TableCell>
                <TableCell>모델</TableCell>
                <TableCell>소스 타입</TableCell>
                <TableCell>히트 수</TableCell>
                <TableCell>생성일</TableCell>
                <TableCell>작업</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {embeddings.map((embedding) => (
                <TableRow key={embedding.id}>
                  <TableCell>
                    <Typography variant="body2" noWrap sx={{ maxWidth: 300 }}>
                      {embedding.text_preview}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Chip 
                      label={embedding.embedding_model} 
                      size="small"
                      color={embedding.embedding_model.includes('gemini') ? 'primary' : 'secondary'}
                    />
                  </TableCell>
                  <TableCell>{embedding.source_type}</TableCell>
                  <TableCell>{embedding.hit_count}</TableCell>
                  <TableCell>
                    {new Date(embedding.created_at).toLocaleDateString()}
                  </TableCell>
                  <TableCell>
                    <Tooltip title="삭제">
                      <IconButton
                        size="small"
                        onClick={() => handleDeleteEmbeddings([embedding.id])}
                      >
                        <DeleteIcon />
                      </IconButton>
                    </Tooltip>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </TabPanel>

      {/* 검색 테스트 탭 */}
      <TabPanel value={tabValue} index={2}>
        <Box sx={{ mb: 3 }}>
          <TextField
            fullWidth
            label="검색 쿼리"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            sx={{ mb: 2 }}
          />
          <Button
            variant="contained"
            startIcon={<SearchIcon />}
            onClick={handleTestSearch}
            disabled={loading || !searchQuery.trim()}
          >
            검색 테스트
          </Button>
        </Box>

        {searchResults && (
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              검색 결과 ({searchResults.results?.length || 0}개)
            </Typography>
            {searchResults.results?.map((result, index) => (
              <Box key={index} sx={{ mb: 2, p: 2, border: 1, borderColor: 'divider', borderRadius: 1 }}>
                <Typography variant="body2" color="textSecondary">
                  점수: {result.score?.toFixed(4)} | 소스: {result.source}
                </Typography>
                <Typography variant="body1" sx={{ mt: 1 }}>
                  {result.text}
                </Typography>
              </Box>
            ))}
          </Paper>
        )}
      </TabPanel>

      {/* 설정 탭 */}
      <TabPanel value={tabValue} index={3}>
        <Box sx={{ mb: 2 }}>
          <Button
            variant="contained"
            onClick={() => setConfigDialog(true)}
          >
            새 설정 추가
          </Button>
        </Box>

        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>이름</TableCell>
                <TableCell>청크 크기</TableCell>
                <TableCell>오버랩</TableCell>
                <TableCell>모델</TableCell>
                <TableCell>상태</TableCell>
                <TableCell>작업</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {configs.map((config) => (
                <TableRow key={config.id}>
                  <TableCell>{config.name}</TableCell>
                  <TableCell>{config.chunk_size}</TableCell>
                  <TableCell>{config.chunk_overlap}</TableCell>
                  <TableCell>{config.embedding_model}</TableCell>
                  <TableCell>
                    <Chip
                      label={config.is_active ? '활성' : '비활성'}
                      color={config.is_active ? 'success' : 'default'}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    {!config.is_active && (
                      <Button
                        size="small"
                        onClick={() => handleActivateConfig(config.id)}
                      >
                        활성화
                      </Button>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </TabPanel>

      {/* 설정 생성 다이얼로그 */}
      <Dialog open={configDialog} onClose={() => setConfigDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>새 임베딩 설정</DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            label="설정 이름"
            value={newConfig.name}
            onChange={(e) => setNewConfig({...newConfig, name: e.target.value})}
            sx={{ mb: 2, mt: 1 }}
          />
          <TextField
            fullWidth
            type="number"
            label="청크 크기"
            value={newConfig.chunk_size}
            onChange={(e) => setNewConfig({...newConfig, chunk_size: parseInt(e.target.value)})}
            sx={{ mb: 2 }}
          />
          <TextField
            fullWidth
            type="number"
            label="청크 오버랩"
            value={newConfig.chunk_overlap}
            onChange={(e) => setNewConfig({...newConfig, chunk_overlap: parseInt(e.target.value)})}
            sx={{ mb: 2 }}
          />
          <FormControl fullWidth>
            <InputLabel>임베딩 모델</InputLabel>
            <Select
              value={newConfig.embedding_model}
              onChange={(e) => setNewConfig({...newConfig, embedding_model: e.target.value})}
            >
              <MenuItem value="models/embedding-001">Gemini Embedding</MenuItem>
              <MenuItem value="text-embedding-3-small">OpenAI Small</MenuItem>
              <MenuItem value="text-embedding-ada-002">OpenAI Ada-002</MenuItem>
            </Select>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setConfigDialog(false)}>취소</Button>
          <Button onClick={handleCreateConfig} variant="contained">생성</Button>
        </DialogActions>
      </Dialog>

      {loading && (
        <Box sx={{ position: 'fixed', top: '50%', left: '50%', transform: 'translate(-50%, -50%)' }}>
          <CircularProgress />
        </Box>
      )}
    </Container>
  );
}
