import time
import logging
from django.shortcuts import get_object_or_404
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from .models import ChatSession, ChatMessage
from .serializers import (
    ChatSessionSerializer,
    ChatSessionListSerializer,
    ChatMessageSerializer,
    SendMessageSerializer
)
from apps.documents.models import Document
from apps.search.hybrid_rag_service import HybridRAGService

logger = logging.getLogger(__name__)


class ChatSessionViewSet(viewsets.ModelViewSet):
    """채팅 세션 ViewSet"""
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return ChatSession.objects.filter(user=self.request.user, is_active=True)

    def get_serializer_class(self):
        if self.action == 'list':
            return ChatSessionListSerializer
        return ChatSessionSerializer

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)

    @action(detail=False, methods=['post'])
    def send_message(self, request):
        """메시지 전송 및 AI 응답 생성"""
        logger.info(f"Received chat request data: {request.data}")

        serializer = SendMessageSerializer(data=request.data)
        if not serializer.is_valid():
            logger.error(f"Serializer validation errors: {serializer.errors}")
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        data = serializer.validated_data
        message_content = data['message']
        session_id = data.get('session_id')
        context = data.get('context', {})

        try:
            # 세션 가져오기 또는 생성
            if session_id:
                session = get_object_or_404(ChatSession, id=session_id, user=request.user)
            else:
                # 새 세션 생성
                document = None
                if context.get('documentId'):
                    try:
                        document = Document.objects.get(id=context['documentId'])
                    except Document.DoesNotExist:
                        pass

                session = ChatSession.objects.create(
                    user=request.user,
                    document=document,
                    title=context.get('documentTitle', '')[:200] if context.get('documentTitle') else ''
                )

            # 사용자 메시지 저장
            user_message = ChatMessage.objects.create(
                session=session,
                sender='user',
                content=message_content,
                context=context
            )

            # AI 응답 생성
            start_time = time.time()
            ai_response = self._generate_ai_response(message_content, context, session)
            response_time = time.time() - start_time

            # AI 메시지 저장
            ai_message = ChatMessage.objects.create(
                session=session,
                sender='ai',
                content=ai_response,
                response_time=response_time,
                model_used='gemini-pro'
            )

            # 세션 업데이트 시간 갱신
            session.save()

            return Response({
                'session_id': session.id,
                'user_message': ChatMessageSerializer(user_message).data,
                'ai_message': ChatMessageSerializer(ai_message).data,
                'response': ai_response
            })

        except Exception as e:
            logger.error(f"Chat error: {str(e)}")
            return Response(
                {'error': '메시지 처리 중 오류가 발생했습니다.'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def _generate_ai_response(self, message, context, session):
        """AI 응답 생성"""
        try:
            rag_service = HybridRAGService()

            # 이전 대화 컨텍스트 구성 (멀티턴 대화)
            conversation_context = ""
            recent_messages = session.messages.order_by('-created_at')[:6]  # 최근 6개 메시지 (3턴)
            if recent_messages:
                conversation_context = "\n\n이전 대화:\n"
                # 시간순으로 정렬 (오래된 것부터)
                for msg in reversed(recent_messages):
                    role = "사용자" if msg.sender == 'user' else "AI"
                    conversation_context += f"{role}: {msg.content}\n"

            # 문서 기반 질문인지 확인
            if context.get('documentId'):
                # 문서 기반 RAG 검색
                search_results = rag_service.search(
                    query=message,
                    k=3,
                    strategy='hybrid'
                )

                # 검색 결과 디버깅
                print(f"RAG 검색 결과: {search_results}")
                print(f"검색 결과 타입: {type(search_results)}")
                if isinstance(search_results, dict):
                    print(f"검색 결과 키들: {search_results.keys()}")
                    if 'combined_results' in search_results:
                        print(f"Combined results 개수: {len(search_results['combined_results'])}")
                        for i, result in enumerate(search_results['combined_results'][:2]):
                            print(f"결과 {i}: {result}")
                    if 'results' in search_results:
                        print(f"Results 개수: {len(search_results['results'])}")
                        for i, result in enumerate(search_results['results'][:2]):
                            print(f"결과 {i}: {result}")

                # 검색 결과를 컨텍스트로 사용
                context_text = ""

                # 하이브리드 검색 결과 처리
                if search_results.get('strategy') == 'hybrid':
                    combined_results = search_results.get('combined_results', [])

                    # 유효한 검색 결과 필터링 (길이가 10자 이상이고 의미있는 내용)
                    valid_results = []
                    for result in combined_results:
                        text = result.get('text', '').strip()
                        if len(text) >= 10 and not text.startswith('파일을미리볼수없습니다'):
                            valid_results.append(result)

                    if valid_results:
                        print(f"유효한 검색 결과 {len(valid_results)}개 발견")
                        for result in valid_results[:3]:  # 상위 3개만 사용
                            context_text += f"관련 내용: {result.get('text', '')}\n\n"
                    else:
                        print("유효한 검색 결과가 없음")

                # 검색 결과가 없거나 유효하지 않은 경우, 프론트엔드에서 전달된 문서 내용 사용
                if not context_text and context.get('documentContent'):
                    print("프론트엔드 문서 내용 사용")
                    context_text = f"문서: {context.get('documentTitle', '')}\n"
                    context_text += f"내용: {context.get('documentContent', '')}\n\n"

                if not context_text:
                    context_text = "참고할 문서 내용이 없습니다.\n"

                # 시스템 프롬프트 구성 (멀티턴 대화 포함)
                system_prompt = f"""당신은 문서 분석 전문 AI 어시스턴트입니다.
다음 문서 내용과 이전 대화를 바탕으로 사용자의 질문에 정확하고 도움이 되는 답변을 제공하세요.

참고 문서:
{context_text}

{conversation_context}

답변 시 다음 사항을 지켜주세요:
1. 문서 내용을 바탕으로 정확한 정보를 제공하세요
2. 이전 대화의 맥락을 고려하여 답변하세요
3. 문서에 없는 내용은 추측하지 마세요
4. 한국어로 자연스럽게 답변하세요
5. 필요시 문서의 어느 부분을 참고했는지 언급하세요"""

                # Gemini API를 통한 응답 생성
                response = rag_service._generate_response_with_context(
                    query=message,
                    context=system_prompt
                )

            else:
                # 일반적인 대화 (멀티턴 지원)
                general_context = f"""당신은 도움이 되는 AI 어시스턴트입니다.
사용자의 질문에 정확하고 친절하게 답변해주세요.

{conversation_context}

이전 대화의 맥락을 고려하여 자연스럽게 답변해주세요."""

                response = rag_service._generate_response_with_context(
                    query=message,
                    context=general_context
                )

            return response

        except Exception as e:
            logger.error(f"AI response generation error: {str(e)}")
            return "죄송합니다. 현재 AI 서비스에 문제가 있어 응답을 생성할 수 없습니다. 잠시 후 다시 시도해주세요."

    @action(detail=False, methods=['delete'])
    def clear_all(self, request):
        """모든 채팅 세션 삭제"""
        deleted_count = ChatSession.objects.filter(user=request.user).update(is_active=False)
        return Response({'deleted_count': deleted_count})
