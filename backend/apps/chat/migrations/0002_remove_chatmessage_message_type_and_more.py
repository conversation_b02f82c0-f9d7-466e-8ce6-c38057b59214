# Generated by Django 4.2.7 on 2025-07-19 16:50

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('documents', '0004_document_file_hash_document_thumbnail'),
        ('chat', '0001_initial'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='chatmessage',
            name='message_type',
        ),
        migrations.RemoveField(
            model_name='chatmessage',
            name='source_documents',
        ),
        migrations.AddField(
            model_name='chatmessage',
            name='context',
            field=models.JSONField(blank=True, help_text='문서 컨텍스트 등 추가 정보', null=True),
        ),
        migrations.AddField(
            model_name='chatmessage',
            name='model_used',
            field=models.CharField(blank=True, help_text='사용된 AI 모델', max_length=50),
        ),
        migrations.AddField(
            model_name='chatmessage',
            name='response_time',
            field=models.FloatField(blank=True, help_text='AI 응답 생성 시간(초)', null=True),
        ),
        migrations.AddField(
            model_name='chatmessage',
            name='sender',
            field=models.CharField(choices=[('user', '사용자'), ('ai', 'AI')], default=1, max_length=10),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='chatmessage',
            name='tokens_used',
            field=models.IntegerField(blank=True, help_text='사용된 토큰 수', null=True),
        ),
        migrations.AddField(
            model_name='chatsession',
            name='document',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='chat_sessions', to='documents.document'),
        ),
        migrations.AddField(
            model_name='chatsession',
            name='is_active',
            field=models.BooleanField(default=True),
        ),
        migrations.DeleteModel(
            name='DocumentReference',
        ),
    ]
