"""
RAG 관리 API 뷰
"""
import logging
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.db.models import Q, Count
from django.core.paginator import Paginator

from apps.documents.models import EmbeddingCache, Document, DocumentChunk
from apps.search.faiss_service import faiss_service
from apps.search.hybrid_rag_service import HybridRAGService
from .models import EmbeddingConfig, EmbeddingStats
from .serializers import (
    EmbeddingCacheSerializer,
    RAGSearchSerializer,
    EmbeddingConfigSerializer,
    EmbeddingStatsSerializer,
    DocumentListSerializer,
    DocumentDetailSerializer,
    DocumentChunkSerializer
)

logger = logging.getLogger(__name__)


class RAGManagementViewSet(viewsets.ViewSet):
    """RAG 시스템 관리 ViewSet"""
    permission_classes = [IsAuthenticated]
    
    @action(detail=False, methods=['get'])
    def embeddings(self, request):
        """임베딩 캐시 목록 조회"""
        try:
            # 필터링 파라미터
            search = request.query_params.get('search', '')
            model = request.query_params.get('model', '')
            source_type = request.query_params.get('source_type', '')
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('page_size', 20))
            
            # 쿼리셋 구성
            queryset = EmbeddingCache.objects.all().order_by('-created_at')
            
            # 필터링
            if search:
                queryset = queryset.filter(
                    Q(original_text__icontains=search) |
                    Q(source_id__icontains=search)
                )
            
            if model:
                queryset = queryset.filter(embedding_model=model)
                
            if source_type:
                queryset = queryset.filter(source_type=source_type)
            
            # 페이지네이션
            paginator = Paginator(queryset, page_size)
            page_obj = paginator.get_page(page)
            
            # 시리얼라이즈
            serializer = EmbeddingCacheSerializer(page_obj.object_list, many=True)
            
            return Response({
                'results': serializer.data,
                'count': paginator.count,
                'num_pages': paginator.num_pages,
                'current_page': page,
                'has_next': page_obj.has_next(),
                'has_previous': page_obj.has_previous(),
            })
            
        except Exception as e:
            logger.error(f"임베딩 목록 조회 오류: {e}")
            return Response(
                {'error': '임베딩 목록을 불러올 수 없습니다.'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=False, methods=['get'])
    def stats(self, request):
        """RAG 시스템 통계"""
        try:
            # 임베딩 통계
            total_embeddings = EmbeddingCache.objects.count()
            gemini_count = EmbeddingCache.objects.filter(
                embedding_model='models/embedding-001'
            ).count()
            openai_count = EmbeddingCache.objects.filter(
                embedding_model='text-embedding-3-small'
            ).count()
            
            # 소스 타입별 통계
            source_stats = {}
            for source_type in ['document', 'notion', 'google_drive', 'query']:
                count = EmbeddingCache.objects.filter(source_type=source_type).count()
                source_stats[source_type] = count
            
            # FAISS 인덱스 상태
            faiss_stats = {
                'gemini_index_size': faiss_service.gemini_index.ntotal if faiss_service.gemini_index else 0,
                'openai_index_size': faiss_service.openai_index.ntotal if faiss_service.openai_index else 0,
                'gemini_metadata_count': len(faiss_service.gemini_metadata),
                'openai_metadata_count': len(faiss_service.openai_metadata),
            }
            
            return Response({
                'embedding_stats': {
                    'total': total_embeddings,
                    'gemini': gemini_count,
                    'openai': openai_count,
                },
                'source_stats': source_stats,
                'faiss_stats': faiss_stats,
            })
            
        except Exception as e:
            logger.error(f"RAG 통계 조회 오류: {e}")
            return Response(
                {'error': 'RAG 통계를 불러올 수 없습니다.'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=False, methods=['post'])
    def rebuild_index(self, request):
        """FAISS 인덱스 재구축"""
        try:
            force_rebuild = request.data.get('force_rebuild', False)
            
            logger.info("FAISS 인덱스 재구축 시작...")
            faiss_service.build_indexes(force_rebuild=force_rebuild)
            
            return Response({
                'message': 'FAISS 인덱스가 성공적으로 재구축되었습니다.',
                'gemini_count': len(faiss_service.gemini_metadata),
                'openai_count': len(faiss_service.openai_metadata),
            })
            
        except Exception as e:
            logger.error(f"FAISS 인덱스 재구축 오류: {e}")
            return Response(
                {'error': 'FAISS 인덱스 재구축에 실패했습니다.'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=False, methods=['post'])
    def test_search(self, request):
        """RAG 검색 테스트"""
        try:
            serializer = RAGSearchSerializer(data=request.data)
            if not serializer.is_valid():
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
            
            query = serializer.validated_data['query']
            k = serializer.validated_data.get('k', 5)
            strategy = serializer.validated_data.get('strategy', 'hybrid')
            
            # RAG 검색 실행
            rag_service = HybridRAGService()
            results = rag_service.search(query=query, k=k, strategy=strategy)
            
            return Response({
                'query': query,
                'strategy': strategy,
                'results': results,
            })
            
        except Exception as e:
            logger.error(f"RAG 검색 테스트 오류: {e}")
            return Response(
                {'error': 'RAG 검색 테스트에 실패했습니다.'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=False, methods=['delete'])
    def delete_embeddings(self, request):
        """임베딩 삭제"""
        try:
            embedding_ids = request.data.get('embedding_ids', [])
            if not embedding_ids:
                return Response(
                    {'error': '삭제할 임베딩 ID를 제공해주세요.'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # 임베딩 삭제
            deleted_count = EmbeddingCache.objects.filter(
                id__in=embedding_ids
            ).delete()[0]
            
            # 인덱스 재구축 (선택적)
            rebuild = request.data.get('rebuild_index', True)
            if rebuild:
                faiss_service.build_indexes(force_rebuild=True)
            
            return Response({
                'message': f'{deleted_count}개의 임베딩이 삭제되었습니다.',
                'deleted_count': deleted_count,
                'index_rebuilt': rebuild,
            })
            
        except Exception as e:
            logger.error(f"임베딩 삭제 오류: {e}")
            return Response(
                {'error': '임베딩 삭제에 실패했습니다.'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=False, methods=['post'])
    def clear_query_cache(self, request):
        """쿼리 캐시 정리"""
        try:
            # 쿼리 타입 임베딩만 삭제
            deleted_count = EmbeddingCache.objects.filter(
                source_type='query'
            ).delete()[0]
            
            # 인덱스 재구축
            faiss_service.build_indexes(force_rebuild=True)
            
            return Response({
                'message': f'{deleted_count}개의 쿼리 캐시가 정리되었습니다.',
                'deleted_count': deleted_count,
            })
            
        except Exception as e:
            logger.error(f"쿼리 캐시 정리 오류: {e}")
            return Response(
                {'error': '쿼리 캐시 정리에 실패했습니다.'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class EmbeddingConfigViewSet(viewsets.ModelViewSet):
    """임베딩 설정 관리 ViewSet"""
    queryset = EmbeddingConfig.objects.all()
    serializer_class = EmbeddingConfigSerializer
    permission_classes = [IsAuthenticated]

    @action(detail=True, methods=['post'])
    def activate(self, request, pk=None):
        """특정 설정을 활성화"""
        config = self.get_object()

        # 다른 모든 설정을 비활성화
        EmbeddingConfig.objects.all().update(is_active=False)

        # 현재 설정을 활성화
        config.is_active = True
        config.save()

        return Response({'status': 'activated'})


class DocumentManagementViewSet(viewsets.ReadOnlyModelViewSet):
    """문서 관리 ViewSet"""
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return Document.objects.filter(created_by=self.request.user).annotate(
            chunks_count=Count('chunks')
        )

    def get_serializer_class(self):
        if self.action == 'retrieve':
            return DocumentDetailSerializer
        return DocumentListSerializer

    @action(detail=True, methods=['get'])
    def chunks(self, request, pk=None):
        """문서의 청크 목록 조회"""
        document = self.get_object()
        chunks = document.chunks.all().order_by('chunk_index')
        serializer = DocumentChunkSerializer(chunks, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def reembed(self, request, pk=None):
        """문서 재임베딩"""
        document = self.get_object()

        try:
            # 현재 활성 설정 가져오기
            active_config = EmbeddingConfig.objects.filter(is_active=True).first()
            if not active_config:
                return Response(
                    {'error': 'No active embedding configuration found'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # 비동기 작업으로 처리
            from apps.documents.tasks import reembed_document
            task = reembed_document.delay(
                document_id=document.id,
                config_id=active_config.id
            )

            return Response({
                'status': 'reembedding started',
                'task_id': task.id,
                'document_title': document.title
            })

        except Exception as e:
            logger.error(f"Error reembedding document {pk}: {str(e)}")
            return Response(
                {'error': 'Failed to start reembedding'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['delete'])
    def delete_embeddings(self, request, pk=None):
        """문서의 임베딩 삭제"""
        document = self.get_object()

        try:
            # 청크의 임베딩 삭제
            document.chunks.update(embedding=None)

            # 임베딩 캐시에서도 삭제
            EmbeddingCache.objects.filter(
                source_type='document',
                source_id=str(document.id)
            ).delete()

            # FAISS 인덱스 재구축
            faiss_service.build_indexes(force_rebuild=True)

            return Response({'status': 'embeddings deleted'})
        except Exception as e:
            logger.error(f"Error deleting embeddings for document {pk}: {str(e)}")
            return Response(
                {'error': 'Failed to delete embeddings'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
