from django.db import models
from django.contrib.auth.models import User
import json


class EmbeddingConfig(models.Model):
    """임베딩 설정을 관리하는 모델"""
    name = models.CharField(max_length=100, unique=True)
    chunk_size = models.IntegerField(default=1000)
    chunk_overlap = models.IntegerField(default=200)
    embedding_model = models.CharField(max_length=100, default='text-embedding-ada-002')
    is_active = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.name} ({'Active' if self.is_active else 'Inactive'})"

    class Meta:
        ordering = ['-is_active', '-updated_at']


class EmbeddingStats(models.Model):
    """임베딩 통계를 관리하는 모델"""
    total_documents = models.IntegerField(default=0)
    total_chunks = models.IntegerField(default=0)
    faiss_index_size = models.IntegerField(default=0)
    openai_index_size = models.IntegerField(default=0)
    last_updated = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name_plural = "Embedding Stats"

    def __str__(self):
        return f"Stats: {self.total_documents} docs, {self.total_chunks} chunks"
