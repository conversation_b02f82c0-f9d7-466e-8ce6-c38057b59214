import axios from 'axios';

// API 기본 설정 - Django 백엔드로 변경
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001/api';

// Axios 인스턴스 생성
export const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 요청 인터셉터 - 인증 토큰 추가
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Token ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 응답 인터셉터 - 에러 처리
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401 || error.response?.status === 403) {
      // 인증 실패 시 로그인 페이지로 리다이렉트
      localStorage.removeItem('auth_token');
      localStorage.removeItem('user');
      window.location.href = '/auth/login';
    }
    return Promise.reject(error);
  }
);

// 문서 관련 API
export const documentsApi = {
  // 문서 목록 조회
  getDocuments: async (params?: {
    search?: string;
    document_type?: string;
    category?: string;
    ordering?: string;
    page?: number;
  }) => {
    const response = await apiClient.get('/documents/documents/', { params });
    return response.data;
  },

  // 문서 상세 조회
  getDocument: async (id: string) => {
    const response = await apiClient.get(`/documents/documents/${id}/`);
    return response.data;
  },

  // 문서 생성
  createDocument: async (data: FormData) => {
    const response = await apiClient.post('/documents/documents/', data, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  // 문서 업데이트
  updateDocument: async (id: string, data: any) => {
    const response = await apiClient.patch(`/documents/documents/${id}/`, data);
    return response.data;
  },

  // 문서 삭제
  deleteDocument: async (id: string) => {
    await apiClient.delete(`/documents/documents/${id}/`);
  },

  // 즐겨찾기 토글
  toggleFavorite: async (id: string) => {
    const response = await apiClient.post(`/documents/documents/${id}/toggle_favorite/`);
    return response.data;
  },

  // 즐겨찾기 문서 목록
  getFavorites: async () => {
    const response = await apiClient.get('/documents/documents/favorites/');
    return response.data;
  },

  // 최근 문서 목록
  getRecent: async () => {
    const response = await apiClient.get('/documents/documents/recent/');
    return response.data;
  },

  // 문서 통계
  getStats: async () => {
    const response = await apiClient.get('/documents/documents/stats/');
    return response.data;
  },

  // 문서 내용 검색
  searchContent: async (query: string, page?: number) => {
    const response = await apiClient.get('/documents/documents/search_content/', {
      params: { q: query, page },
    });
    return response.data;
  },

  // 임시 파일 업로드
  tempUpload: async (file: File, metadata?: {
    title?: string;
    description?: string;
    document_type?: string;
  }) => {
    const formData = new FormData();
    formData.append('file', file);
    if (metadata?.title) formData.append('title', metadata.title);
    if (metadata?.description) formData.append('description', metadata.description);
    if (metadata?.document_type) formData.append('document_type', metadata.document_type);

    const response = await apiClient.post('/documents/documents/temp_upload/', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  // 임시 파일 상태 조회
  getTempStatus: async (fileId: string) => {
    const response = await apiClient.get('/documents/documents/temp_status/', {
      params: { file_id: fileId },
    });
    return response.data;
  },

  // 임시 파일 목록 조회
  getTempList: async (status?: string) => {
    const response = await apiClient.get('/documents/documents/temp_list/', {
      params: status ? { status } : {},
    });
    return response.data;
  },

  // 임시 파일 처리 (동기)
  processTempFile: async (fileId: string) => {
    const response = await apiClient.post('/documents/documents/process_temp_file/', {
      file_id: fileId,
    });
    return response.data;
  },

  // 임시 파일 처리 (비동기)
  processTempFileAsync: async (fileId: string) => {
    const response = await apiClient.post('/documents/documents/process_temp_file_async/', {
      file_id: fileId,
    });
    return response.data;
  },

  // 태스크 상태 조회
  getTaskStatus: async (taskId: string) => {
    const response = await apiClient.get('/documents/documents/task_status/', {
      params: { task_id: taskId },
    });
    return response.data;
  },

  // 저장소 통계
  getStorageStats: async () => {
    const response = await apiClient.get('/documents/documents/storage_stats/');
    return response.data;
  },

  // 청크 생성
  createChunks: async (documentId: string, forceRecreate?: boolean) => {
    const response = await apiClient.post(`/documents/documents/${documentId}/create_chunks/`, {
      force_recreate: forceRecreate || false,
    });
    return response.data;
  },

  // 임베딩 생성
  generateEmbeddings: async (documentId: string, model?: string) => {
    const response = await apiClient.post(`/documents/documents/${documentId}/generate_embeddings/`, {
      model: model || 'models/embedding-001',
    });
    return response.data;
  },

  // 청크 통계
  getChunkStats: async () => {
    const response = await apiClient.get('/documents/documents/chunk_stats/');
    return response.data;
  },
};

// 검색 관련 API
export const searchApi = {
  // 벡터 검색
  vectorSearch: async (query: string, params?: {
    top_k?: number;
    threshold?: number;
    document_types?: string[];
  }) => {
    const response = await apiClient.post('/search/vector_search/', {
      query,
      ...params,
    });
    return response.data;
  },

  // 하이브리드 검색
  hybridSearch: async (query: string, params?: {
    top_k?: number;
    alpha?: number;
    document_types?: string[];
  }) => {
    const response = await apiClient.post('/search/hybrid_search/', {
      query,
      ...params,
    });
    return response.data;
  },
};

// 카테고리 관련 API
export const categoriesApi = {
  getCategories: async () => {
    const response = await apiClient.get('/categories/');
    return response.data;
  },
};

// 태그 관련 API
export const tagsApi = {
  getTags: async () => {
    const response = await apiClient.get('/tags/');
    return response.data;
  },
};

// 채팅 관련 API
export const chatApi = {
  // 채팅 세션 목록 조회
  getSessions: async () => {
    const response = await apiClient.get('/chat/sessions/');
    return response.data;
  },

  // 특정 채팅 세션 조회
  getSession: async (sessionId: string) => {
    const response = await apiClient.get(`/chat/sessions/${sessionId}/`);
    return response.data;
  },

  // 메시지 전송
  sendMessage: async (data: {
    message: string;
    sessionId?: string;
    context?: {
      documentId?: string;
      documentTitle?: string;
      documentContent?: string;
    };
  }) => {
    const response = await apiClient.post('/chat/sessions/send_message/', {
      message: data.message,
      session_id: data.sessionId ? parseInt(data.sessionId) : undefined,
      context: data.context,
    });
    return response.data;
  },

  // 채팅 세션 삭제
  deleteSession: async (sessionId: string) => {
    await apiClient.delete(`/chat/sessions/${sessionId}/`);
  },

  // 모든 채팅 세션 삭제
  clearAll: async () => {
    await apiClient.delete('/chat/sessions/clear_all/');
  },
};

// 인증 관련 API
export const authApi = {
  login: async (username: string, password: string) => {
    const response = await apiClient.post('/auth/login/', {
      username,
      password,
    });
    return response.data;
  },

  register: async (name: string, email: string, password: string) => {
    // 회원가입은 별도 구현 필요
    throw new Error('Registration not implemented yet');
  },
};

export default apiClient;
