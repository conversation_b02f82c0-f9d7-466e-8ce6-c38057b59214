"""
임베딩 서비스 - 캐시를 활용한 중복 방지 임베딩 생성
"""
import os
import logging
from typing import List, Tuple, Optional
# 조건부 import
try:
    import google.generativeai as genai
    GEMINI_AVAILABLE = True
except ImportError:
    genai = None
    GEMINI_AVAILABLE = False

try:
    from openai import OpenAI
    OPENAI_AVAILABLE = True
except ImportError:
    OpenAI = None
    OPENAI_AVAILABLE = False
from django.conf import settings
from django.db import models
from .models import EmbeddingCache

logger = logging.getLogger(__name__)


class EmbeddingService:
    """임베딩 생성 및 캐시 관리 서비스"""
    
    def __init__(self):
        # Gemini API 설정
        if GEMINI_AVAILABLE:
            gemini_key = os.getenv('GEMINI_API_KEY')
            if gemini_key:
                genai.configure(api_key=gemini_key)
                self.gemini_available = True
            else:
                self.gemini_available = False
                logger.warning("Gemini API 키가 설정되지 않았습니다.")
        else:
            self.gemini_available = False
            logger.warning("google-generativeai 모듈이 설치되지 않았습니다.")

        # OpenAI API 설정
        if OPENAI_AVAILABLE:
            openai_key = os.getenv('OPENAI_API_KEY')
            if openai_key:
                self.openai_client = OpenAI(api_key=openai_key)
                self.openai_available = True
            else:
                self.openai_client = None
                self.openai_available = False
                logger.warning("OpenAI API 키가 설정되지 않았습니다.")
        else:
            self.openai_client = None
            self.openai_available = False
            logger.warning("openai 모듈이 설치되지 않았습니다.")
    
    def generate_embedding(self, text: str, model: str = 'models/embedding-001',
                          source_type: str = '', source_id: str = '') -> Optional[List[float]]:
        """
        텍스트에서 임베딩 생성 (캐시 우선)
        
        Args:
            text: 임베딩할 텍스트
            model: 사용할 임베딩 모델
            source_type: 소스 타입 (notion, document 등)
            source_id: 소스 ID
            
        Returns:
            임베딩 벡터 또는 None (실패시)
        """
        if not text.strip():
            logger.warning("빈 텍스트는 임베딩할 수 없습니다.")
            return None
        
        # 1. 캐시에서 확인
        cached_embedding, is_cache_hit = EmbeddingCache.get_or_create_embedding(
            text=text,
            embedding_model=model,
            source_type=source_type,
            source_id=source_id
        )
        
        if is_cache_hit:
            logger.info(f"캐시 히트: {source_type}:{source_id}")
            return cached_embedding
        
        # 2. 새로 생성
        try:
            embedding_vector = self._generate_new_embedding(text, model)
            if embedding_vector:
                # 캐시에 저장
                self._save_to_cache(
                    text=text,
                    embedding_vector=embedding_vector,
                    model=model,
                    source_type=source_type,
                    source_id=source_id
                )
                logger.info(f"새 임베딩 생성: {source_type}:{source_id}")
                return embedding_vector
            
        except Exception as e:
            logger.error(f"임베딩 생성 실패: {e}")
            return None
        
        return None
    
    def _generate_new_embedding(self, text: str, model: str) -> Optional[List[float]]:
        """새 임베딩 생성 (실제 API 호출)"""

        # OpenAI 모델인지 확인
        if model.startswith('text-embedding'):
            if not self.openai_available:
                logger.error("OpenAI API를 사용할 수 없습니다.")
                return None

            try:
                response = self.openai_client.embeddings.create(
                    model=model,
                    input=text
                )
                return response.data[0].embedding

            except Exception as e:
                logger.error(f"OpenAI API 호출 실패: {e}")
                return None

        # Gemini 모델
        else:
            if not self.gemini_available:
                logger.error("Gemini API를 사용할 수 없습니다.")
                return None

            try:
                result = genai.embed_content(
                    model=model,
                    content=text,
                    task_type='retrieval_document'
                )
                return result['embedding']

            except Exception as e:
                logger.error(f"Gemini API 호출 실패: {e}")
                return None
    
    def _save_to_cache(self, text: str, embedding_vector: List[float], 
                      model: str, source_type: str, source_id: str):
        """임베딩을 캐시에 저장"""
        try:
            cache_entry = EmbeddingCache()
            cache_entry.save_embedding(
                text=text,
                embedding_vector=embedding_vector,
                embedding_model=model,
                source_type=source_type,
                source_id=source_id
            )
            logger.debug(f"캐시 저장 완료: {cache_entry.content_hash[:8]}")
            
        except Exception as e:
            logger.error(f"캐시 저장 실패: {e}")
    
    def batch_generate_embeddings(self, texts: List[str], model: str = 'models/embedding-001',
                                 source_type: str = '', source_ids: List[str] = None) -> List[Optional[List[float]]]:
        """
        여러 텍스트의 임베딩을 배치로 생성
        
        Args:
            texts: 임베딩할 텍스트 리스트
            model: 사용할 임베딩 모델
            source_type: 소스 타입
            source_ids: 각 텍스트의 소스 ID 리스트
            
        Returns:
            임베딩 벡터 리스트
        """
        if source_ids is None:
            source_ids = [''] * len(texts)
        
        embeddings = []
        cache_hits = 0
        new_generations = 0
        
        for i, text in enumerate(texts):
            source_id = source_ids[i] if i < len(source_ids) else ''
            embedding = self.generate_embedding(
                text=text,
                model=model,
                source_type=source_type,
                source_id=source_id
            )
            embeddings.append(embedding)
            
            if embedding:
                # 캐시 히트 여부 확인 (로그에서 판단)
                pass
        
        logger.info(f"배치 임베딩 완료: 총 {len(texts)}개, 성공 {sum(1 for e in embeddings if e is not None)}개")
        return embeddings
    
    def get_cache_stats(self) -> dict:
        """캐시 통계 조회"""
        try:
            total_entries = EmbeddingCache.objects.count()
            total_hits = EmbeddingCache.objects.aggregate(
                total_hits=models.Sum('hit_count')
            )['total_hits'] or 0
            
            model_stats = EmbeddingCache.objects.values('embedding_model').annotate(
                count=models.Count('id'),
                hits=models.Sum('hit_count')
            )
            
            return {
                'total_entries': total_entries,
                'total_hits': total_hits,
                'models': list(model_stats)
            }
            
        except Exception as e:
            logger.error(f"캐시 통계 조회 실패: {e}")
            return {}
    
    def clear_cache(self, older_than_days: int = None, model: str = None):
        """캐시 정리"""
        try:
            queryset = EmbeddingCache.objects.all()
            
            if model:
                queryset = queryset.filter(embedding_model=model)
            
            if older_than_days:
                from django.utils import timezone
                from datetime import timedelta
                cutoff_date = timezone.now() - timedelta(days=older_than_days)
                queryset = queryset.filter(created_at__lt=cutoff_date)
            
            deleted_count = queryset.count()
            queryset.delete()
            
            logger.info(f"캐시 정리 완료: {deleted_count}개 항목 삭제")
            return deleted_count
            
        except Exception as e:
            logger.error(f"캐시 정리 실패: {e}")
            return 0


# 전역 인스턴스
embedding_service = EmbeddingService()
