from rest_framework import serializers
from .models import EmbeddingConfig, EmbeddingStats
from apps.documents.models import Document, DocumentChunk, EmbeddingCache


class EmbeddingConfigSerializer(serializers.ModelSerializer):
    class Meta:
        model = EmbeddingConfig
        fields = '__all__'


class EmbeddingStatsSerializer(serializers.ModelSerializer):
    class Meta:
        model = EmbeddingStats
        fields = '__all__'


class DocumentListSerializer(serializers.ModelSerializer):
    chunks_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Document
        fields = ['id', 'title', 'file_type', 'created_at', 'updated_at', 'chunks_count']
    
    def get_chunks_count(self, obj):
        return obj.chunks.count()


class DocumentChunkSerializer(serializers.ModelSerializer):
    has_embedding = serializers.SerializerMethodField()
    
    class Meta:
        model = DocumentChunk
        fields = ['id', 'content', 'chunk_index', 'created_at', 'has_embedding']
    
    def get_has_embedding(self, obj):
        return obj.embedding is not None


class DocumentDetailSerializer(serializers.ModelSerializer):
    chunks = DocumentChunkSerializer(many=True, read_only=True)
    chunks_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Document
        fields = ['id', 'title', 'content', 'file_type', 'created_at', 'updated_at', 'chunks_count', 'chunks']
    
    def get_chunks_count(self, obj):
        return obj.chunks.count()


class EmbeddingCacheSerializer(serializers.ModelSerializer):
    """임베딩 캐시 시리얼라이저"""
    text_preview = serializers.SerializerMethodField()

    class Meta:
        model = EmbeddingCache
        fields = [
            'id', 'content_hash', 'text_preview', 'embedding_model',
            'vector_dimension', 'source_type', 'source_id',
            'hit_count', 'last_accessed', 'created_at'
        ]

    def get_text_preview(self, obj):
        """텍스트 미리보기 (처음 100자)"""
        return obj.original_text[:100] + '...' if len(obj.original_text) > 100 else obj.original_text


class RAGSearchSerializer(serializers.Serializer):
    """RAG 검색 요청 시리얼라이저"""
    query = serializers.CharField(max_length=1000)
    k = serializers.IntegerField(default=5, min_value=1, max_value=20)
    strategy = serializers.ChoiceField(
        choices=['hybrid', 'faiss_only', 'openai_only'],
        default='hybrid'
    )
