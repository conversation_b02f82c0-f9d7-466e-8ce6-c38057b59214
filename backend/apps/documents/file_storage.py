"""
개선된 파일 저장 시스템
- 파일 중복 제거 (해시 기반)
- 안전한 파일명 생성
- 파일 타입별 분류 저장
- 썸네일 생성 (이미지/비디오)
- 메타데이터 추출
"""
import os
import hashlib
import uuid
from pathlib import Path
from typing import Dict, Any, Optional, Tuple
from django.conf import settings
from django.core.files.base import ContentFile
from django.core.files.storage import default_storage
from PIL import Image
import logging

logger = logging.getLogger(__name__)


class FileStorageManager:
    """파일 저장 관리자"""
    
    def __init__(self):
        self.media_root = Path(settings.MEDIA_ROOT)
        self.supported_image_formats = {'.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp'}
        self.supported_video_formats = {'.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv'}
        self.supported_document_formats = {'.pdf', '.doc', '.docx', '.txt', '.md', '.rtf'}
        
    def calculate_file_hash(self, content: bytes) -> str:
        """파일 내용의 SHA-256 해시 계산"""
        return hashlib.sha256(content).hexdigest()
    
    def generate_safe_filename(self, original_filename: str, file_hash: str) -> str:
        """안전한 파일명 생성"""
        # 파일 확장자 추출
        file_ext = Path(original_filename).suffix.lower()
        
        # 해시의 앞 16자리 + 확장자로 파일명 생성
        safe_filename = f"{file_hash[:16]}{file_ext}"
        return safe_filename
    
    def get_file_category(self, filename: str, content_type: str) -> str:
        """파일 카테고리 결정"""
        file_ext = Path(filename).suffix.lower()
        
        if file_ext in self.supported_image_formats:
            return 'images'
        elif file_ext in self.supported_video_formats:
            return 'videos'
        elif file_ext in self.supported_document_formats:
            return 'documents'
        elif content_type.startswith('audio/'):
            return 'audio'
        else:
            return 'others'
    
    def get_storage_path(self, category: str, filename: str) -> str:
        """저장 경로 생성"""
        from datetime import datetime
        now = datetime.now()
        
        # 카테고리/년/월/파일명 구조
        return f"{category}/{now.year}/{now.month:02d}/{filename}"
    
    def create_thumbnail(self, image_path: str, size: Tuple[int, int] = (300, 300)) -> Optional[str]:
        """이미지 썸네일 생성"""
        try:
            full_path = self.media_root / image_path
            if not full_path.exists():
                return None
            
            # 썸네일 경로 생성
            thumb_dir = full_path.parent / 'thumbnails'
            thumb_dir.mkdir(exist_ok=True)
            
            thumb_filename = f"thumb_{full_path.name}"
            thumb_path = thumb_dir / thumb_filename
            
            # 썸네일 생성
            with Image.open(full_path) as img:
                # RGBA 모드인 경우 RGB로 변환
                if img.mode in ('RGBA', 'LA', 'P'):
                    img = img.convert('RGB')
                
                img.thumbnail(size, Image.Resampling.LANCZOS)
                img.save(thumb_path, 'JPEG', quality=85)
            
            # 상대 경로 반환
            relative_thumb_path = str(thumb_path.relative_to(self.media_root))
            return relative_thumb_path
            
        except Exception as e:
            logger.error(f"썸네일 생성 실패: {image_path} - {str(e)}")
            return None
    
    def extract_file_metadata(self, file_path: str, content_type: str) -> Dict[str, Any]:
        """파일 메타데이터 추출"""
        metadata = {
            'content_type': content_type,
            'size': 0,
            'dimensions': None,
            'duration': None,
        }
        
        try:
            full_path = self.media_root / file_path
            if not full_path.exists():
                return metadata
            
            metadata['size'] = full_path.stat().st_size
            
            # 이미지 메타데이터
            if content_type.startswith('image/'):
                try:
                    with Image.open(full_path) as img:
                        metadata['dimensions'] = {
                            'width': img.width,
                            'height': img.height
                        }
                        metadata['format'] = img.format
                except Exception as e:
                    logger.warning(f"이미지 메타데이터 추출 실패: {file_path} - {str(e)}")
            
            # 비디오 메타데이터는 추후 ffmpeg 등으로 구현 가능
            
        except Exception as e:
            logger.error(f"메타데이터 추출 실패: {file_path} - {str(e)}")
        
        return metadata
    
    def store_file(self, 
                   filename: str, 
                   content: bytes, 
                   content_type: str,
                   check_duplicate: bool = True) -> Dict[str, Any]:
        """
        파일 저장
        
        Args:
            filename: 원본 파일명
            content: 파일 내용
            content_type: 콘텐츠 타입
            check_duplicate: 중복 파일 체크 여부
            
        Returns:
            Dict: 저장된 파일 정보
        """
        try:
            # 파일 해시 계산
            file_hash = self.calculate_file_hash(content)
            
            # 중복 파일 체크
            if check_duplicate:
                existing_file = self.find_existing_file(file_hash)
                if existing_file:
                    logger.info(f"중복 파일 발견: {filename} -> {existing_file['path']}")
                    return existing_file
            
            # 안전한 파일명 생성
            safe_filename = self.generate_safe_filename(filename, file_hash)
            
            # 파일 카테고리 결정
            category = self.get_file_category(filename, content_type)
            
            # 저장 경로 생성
            storage_path = self.get_storage_path(category, safe_filename)
            
            # 파일 저장
            file_content = ContentFile(content, name=safe_filename)
            saved_path = default_storage.save(storage_path, file_content)
            
            # 썸네일 생성 (이미지인 경우)
            thumbnail_path = None
            if category == 'images':
                thumbnail_path = self.create_thumbnail(saved_path)
            
            # 메타데이터 추출
            metadata = self.extract_file_metadata(saved_path, content_type)
            
            # 파일 정보 반환
            file_info = {
                'path': saved_path,
                'url': default_storage.url(saved_path),
                'filename': safe_filename,
                'original_filename': filename,
                'size': len(content),
                'content_type': content_type,
                'category': category,
                'hash': file_hash,
                'thumbnail_path': thumbnail_path,
                'thumbnail_url': default_storage.url(thumbnail_path) if thumbnail_path else None,
                'metadata': metadata
            }
            
            logger.info(f"파일 저장 완료: {filename} -> {saved_path}")
            return file_info
            
        except Exception as e:
            logger.error(f"파일 저장 실패: {filename} - {str(e)}")
            raise
    
    def find_existing_file(self, file_hash: str) -> Optional[Dict[str, Any]]:
        """해시로 기존 파일 찾기 (추후 데이터베이스 연동)"""
        # 현재는 간단한 구현, 추후 데이터베이스에서 해시로 검색
        # Document 모델에 file_hash 필드 추가 후 구현
        return None
    
    def delete_file(self, file_path: str, delete_thumbnail: bool = True) -> bool:
        """파일 삭제"""
        try:
            # 원본 파일 삭제
            if default_storage.exists(file_path):
                default_storage.delete(file_path)
                logger.info(f"파일 삭제 완료: {file_path}")
            
            # 썸네일 삭제
            if delete_thumbnail:
                thumb_path = self.get_thumbnail_path(file_path)
                if thumb_path and default_storage.exists(thumb_path):
                    default_storage.delete(thumb_path)
                    logger.info(f"썸네일 삭제 완료: {thumb_path}")
            
            return True
            
        except Exception as e:
            logger.error(f"파일 삭제 실패: {file_path} - {str(e)}")
            return False
    
    def get_thumbnail_path(self, file_path: str) -> Optional[str]:
        """파일 경로에서 썸네일 경로 생성"""
        try:
            path = Path(file_path)
            thumb_path = path.parent / 'thumbnails' / f"thumb_{path.name}"
            return str(thumb_path)
        except:
            return None
    
    def get_storage_stats(self) -> Dict[str, Any]:
        """저장소 통계"""
        try:
            total_size = 0
            file_count = 0
            category_stats = {}
            
            for category in ['images', 'videos', 'documents', 'audio', 'others']:
                category_path = self.media_root / category
                if category_path.exists():
                    category_size = 0
                    category_count = 0
                    
                    for file_path in category_path.rglob('*'):
                        if file_path.is_file():
                            file_size = file_path.stat().st_size
                            category_size += file_size
                            category_count += 1
                    
                    category_stats[category] = {
                        'count': category_count,
                        'size_bytes': category_size,
                        'size_mb': round(category_size / (1024 * 1024), 2)
                    }
                    
                    total_size += category_size
                    file_count += category_count
            
            return {
                'total_files': file_count,
                'total_size_bytes': total_size,
                'total_size_mb': round(total_size / (1024 * 1024), 2),
                'by_category': category_stats
            }
            
        except Exception as e:
            logger.error(f"저장소 통계 조회 실패: {str(e)}")
            return {}


# 전역 파일 저장 관리자 인스턴스
file_storage_manager = FileStorageManager()


def get_file_storage_manager() -> FileStorageManager:
    """파일 저장 관리자 인스턴스 반환"""
    return file_storage_manager
